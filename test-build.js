// Test build script to verify the extension builds correctly
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing G Maps Extractor Pro Build...');
console.log('=' * 50);

try {
  // Clean dist directory
  console.log('🧹 Cleaning dist directory...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // Run build
  console.log('🔨 Building extension...');
  execSync('npm run build', { stdio: 'inherit' });

  // Check if all required files are generated
  console.log('\n📋 Checking generated files...');
  
  const requiredFiles = [
    'dist/manifest.json',
    'dist/popup.html',
    'dist/sidebar.html',
    'dist/popup.js',
    'dist/sidebar.js',
    'dist/background.js',
    'dist/content.js',
    'dist/injected.js',
    'dist/icon16.png',
    'dist/icon48.png',
    'dist/icon128.png'
  ];

  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    const status = exists ? '✅' : '❌';
    console.log(`${status} ${file}`);
    
    if (!exists) {
      allFilesExist = false;
    } else {
      // Check file size
      const stats = fs.statSync(file);
      const sizeKB = (stats.size / 1024).toFixed(2);
      console.log(`    Size: ${sizeKB} KB`);
    }
  });

  // Check injected.js format (should be IIFE to avoid CSP issues)
  console.log('\n🔍 Checking injected.js format...');
  if (fs.existsSync('dist/injected.js')) {
    const injectedContent = fs.readFileSync('dist/injected.js', 'utf8');
    
    // Check if it's IIFE format (should start with (function() or similar)
    const isIIFE = injectedContent.includes('(function()') || 
                   injectedContent.includes('(function ') ||
                   injectedContent.includes('!function') ||
                   injectedContent.includes('var GMapsExtractorInjected');
    
    console.log(`✓ IIFE Format: ${isIIFE ? '✅' : '❌'}`);
    
    // Check if it contains our API
    const hasAPI = injectedContent.includes('GMapsExtractorAPI');
    console.log(`✓ Contains API: ${hasAPI ? '✅' : '❌'}`);
    
    // Check if it has no ES6 imports/exports (should be self-contained)
    const hasESModules = injectedContent.includes('import ') || 
                        injectedContent.includes('export ');
    console.log(`✓ No ES Modules: ${!hasESModules ? '✅' : '❌'}`);
    
    if (!isIIFE || !hasAPI || hasESModules) {
      console.log('⚠️  Injected script may have issues');
      console.log('First 200 characters:');
      console.log(injectedContent.substring(0, 200) + '...');
    }
  }

  // Check manifest.json
  console.log('\n📋 Checking manifest.json...');
  if (fs.existsSync('dist/manifest.json')) {
    const manifest = JSON.parse(fs.readFileSync('dist/manifest.json', 'utf8'));
    
    console.log(`✓ Manifest Version: ${manifest.manifest_version}`);
    console.log(`✓ Extension Name: ${manifest.name}`);
    console.log(`✓ Extension Version: ${manifest.version}`);
    
    // Check if injected.js is in web_accessible_resources
    const hasInjectedResource = manifest.web_accessible_resources?.some(
      resource => resource.resources?.includes('injected.js')
    );
    console.log(`✓ Injected.js in web_accessible_resources: ${hasInjectedResource ? '✅' : '❌'}`);
  }

  // Summary
  console.log('\n📊 Build Summary:');
  console.log('=' * 30);
  
  if (allFilesExist) {
    console.log('🎉 All required files generated successfully!');
    console.log('💡 Extension is ready for testing.');
    console.log('\n📝 Next steps:');
    console.log('1. Open Chrome and go to chrome://extensions/');
    console.log('2. Enable "Developer mode"');
    console.log('3. Click "Load unpacked" and select the dist/ folder');
    console.log('4. Test the extension on Google Maps');
  } else {
    console.log('❌ Some files are missing. Check the build process.');
  }

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

console.log('\n✅ Build test completed!');
