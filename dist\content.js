function generateId() {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}
function sanitizeBusinessData(business) {
  return {
    ...business,
    name: business.name?.trim() || "",
    address: business.address?.trim() || "",
    phone: business.phone?.trim(),
    website: business.website?.trim(),
    email: business.email?.trim()
  };
}
class GoogleMapsExtractor {
  isExtracting = false;
  extractedBusinesses = [];
  currentSearchParams = null;
  constructor() {
    this.init();
    this.injectScript();
  }
  injectScript() {
    try {
      const script = document.createElement("script");
      script.src = chrome.runtime.getURL("injected.iife.js");
      script.onload = () => {
        console.log("Injected script loaded successfully");
        script.remove();
      };
      script.onerror = (error) => {
        console.error("Failed to load injected script:", error);
      };
      (document.head || document.documentElement).appendChild(script);
    } catch (error) {
      console.error("Failed to inject script:", error);
    }
  }
  init() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log("Content script received message:", message.type);
      this.handleMessage(message, sendResponse);
      return true;
    });
    this.injectHelperScript();
    console.log("Google Maps Extractor content script loaded on:", window.location.href);
    setTimeout(() => {
      console.log("Content script initialization complete");
    }, 1e3);
  }
  async handleMessage(message, sendResponse) {
    try {
      switch (message.type) {
        case "PING":
          sendResponse({ success: true, message: "Content script is ready" });
          break;
        case "START_EXTRACTION":
          await this.startExtraction(message.payload, sendResponse);
          break;
        case "EXTRACT_REVIEWS":
          await this.extractReviews(message.payload, sendResponse);
          break;
        case "EXTRACT_PHOTOS":
          await this.extractPhotos(message.payload, sendResponse);
          break;
        case "GET_CURRENT_BUSINESS":
          await this.getCurrentBusiness(sendResponse);
          break;
        default:
          sendResponse({ success: false, error: "Unknown message type" });
      }
    } catch (error) {
      console.error("Content script error:", error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Unknown error" });
    }
  }
  injectHelperScript() {
    const script = document.createElement("script");
    script.src = chrome.runtime.getURL("injected.iife.js");
    script.onload = () => script.remove();
    (document.head || document.documentElement).appendChild(script);
    this.injectInternalAPIScript();
  }
  injectInternalAPIScript() {
    const script = document.createElement("script");
    script.src = chrome.runtime.getURL("internal-api.iife.js");
    script.onload = () => {
      console.log("Internal API script loaded successfully");
      script.remove();
    };
    script.onerror = (error) => {
      console.error("Failed to load internal API script:", error);
    };
    (document.head || document.documentElement).appendChild(script);
  }
  async startExtraction(params, sendResponse) {
    if (this.isExtracting) {
      sendResponse({ success: false, error: "Extraction already in progress" });
      return;
    }
    try {
      this.isExtracting = true;
      this.currentSearchParams = params.searchParams;
      this.extractedBusinesses = [];
      if (!this.isGoogleMapsPage()) {
        throw new Error("Please navigate to Google Maps first");
      }
      if (params.searchParams.query) {
        await this.performSearch(params.searchParams.query);
        await this.waitForSearchResults();
      }
      const businesses = await this.extractBusinessesFromResults(params.searchParams, params.filterOptions);
      sendResponse({ success: true, data: businesses });
      chrome.runtime.sendMessage({
        type: "BUSINESSES_EXTRACTED",
        payload: businesses
      });
    } catch (error) {
      console.error("Extraction failed:", error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Extraction failed" });
    } finally {
      this.isExtracting = false;
    }
  }
  isGoogleMapsPage() {
    return window.location.hostname.includes("google") && (window.location.pathname.includes("/maps") || window.location.hostname.includes("maps.google"));
  }
  async performSearch(query) {
    console.log("Performing search for:", query);
    const searchSelectors = [
      "#searchboxinput",
      'input[aria-label*="Search"]',
      'input[placeholder*="Search"]',
      'input[data-value="Search"]',
      'input[jsaction*="paste"]',
      'input[autocomplete="off"]',
      'form input[type="text"]'
    ];
    let searchInput = null;
    for (const selector of searchSelectors) {
      searchInput = document.querySelector(selector);
      if (searchInput && searchInput.offsetParent !== null) {
        console.log("Found search input with selector:", selector);
        break;
      }
    }
    if (!searchInput) {
      throw new Error("Search input not found. Make sure you are on Google Maps search page.");
    }
    await this.performAdvancedSearch(searchInput, query);
    console.log("Search submitted");
  }
  async performAdvancedSearch(searchInput, query) {
    searchInput.value = "";
    searchInput.focus();
    await this.delay(300);
    for (let i = 0; i < query.length; i++) {
      searchInput.value = query.substring(0, i + 1);
      searchInput.dispatchEvent(new Event("input", { bubbles: true }));
      searchInput.dispatchEvent(new Event("keyup", { bubbles: true }));
      await this.delay(50 + Math.random() * 50);
    }
    await this.delay(500);
    searchInput.dispatchEvent(new Event("change", { bubbles: true }));
    searchInput.dispatchEvent(new Event("blur", { bubbles: true }));
    await this.delay(300);
    const submitMethods = [
      () => {
        const searchButton = document.querySelector("#searchbox-searchbutton");
        if (searchButton && searchButton.offsetParent !== null) {
          searchButton.click();
          return true;
        }
        return false;
      },
      () => {
        const searchForm = searchInput.closest("form");
        if (searchForm) {
          searchForm.submit();
          return true;
        }
        return false;
      },
      () => {
        searchInput.dispatchEvent(new KeyboardEvent("keydown", {
          key: "Enter",
          code: "Enter",
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true
        }));
        return true;
      }
    ];
    for (const method of submitMethods) {
      if (method()) {
        console.log("Search submitted successfully");
        break;
      }
      await this.delay(200);
    }
  }
  async waitForSearchResults() {
    console.log("Waiting for search results...");
    const maxWait = 2e4;
    const startTime = Date.now();
    const resultSelectors = [
      ".Nv2PK.THOPZb.CpccDe",
      // Main business result container (exact match from HTML)
      ".Nv2PK",
      // Fallback - main container
      ".bfdHYd.Ppzolf.OFBs3e",
      // Business content container
      ".lI9IFe",
      // Inner content container
      "a.hfpxzc",
      // Business link container
      '[jsaction*="pane.wfvdle"]',
      // Interactive elements with specific actions
      'div[jsaction*="mouseover:pane"]',
      // Mouseover interactive elements
      "[data-result-index]",
      // Traditional result index
      '[role="article"]',
      // Article role elements
      'a[href*="/place/"]'
      // Place links
    ];
    let foundResults = false;
    while (Date.now() - startTime < maxWait) {
      for (const selector of resultSelectors) {
        const results = document.querySelectorAll(selector);
        if (results.length > 0) {
          console.log(`Found ${results.length} results with selector: ${selector}`);
          foundResults = true;
          break;
        }
      }
      const placeLinks = document.querySelectorAll('a[href*="/place/"]');
      const businessNames = document.querySelectorAll('[data-value*="directions"]');
      if (placeLinks.length > 0 || businessNames.length > 0) {
        console.log(`Found ${placeLinks.length} place links and ${businessNames.length} business elements`);
        foundResults = true;
      }
      if (foundResults) {
        await this.delay(3e3);
        return;
      }
      const noResultsSelectors = [
        '[data-value="No results"]',
        ".section-no-result",
        ".gm-err-container",
        '[aria-label*="No results"]'
      ];
      for (const selector of noResultsSelectors) {
        if (document.querySelector(selector)) {
          throw new Error("No search results found for this query");
        }
      }
      await this.delay(1e3);
    }
    throw new Error("Search results did not load in time. Try refreshing the page and searching again.");
  }
  async extractBusinessesFromResults(searchParams, filterOptions) {
    const businesses = [];
    const maxResults = Math.min(searchParams.maxResults || 20, 100);
    console.log("Starting enhanced business extraction...");
    const injectedData = await this.extractUsingInjectedScript();
    if (injectedData && injectedData.length > 0) {
      console.log(`Extracted ${injectedData.length} businesses using injected script`);
      businesses.push(...injectedData.slice(0, maxResults));
    }
    if (businesses.length < maxResults) {
      console.log("Supplementing with DOM extraction...");
      const domBusinesses = await this.extractFromDOM(maxResults - businesses.length, searchParams);
      businesses.push(...domBusinesses);
    }
    if (businesses.length === 0) {
      console.log("No business results found. Trying to extract from current view...");
      const currentBusiness = await this.extractCurrentBusinessView(searchParams);
      if (currentBusiness) {
        businesses.push(currentBusiness);
      }
    }
    const filteredBusinesses = this.applyFilters(businesses, filterOptions);
    console.log(`Extraction complete. Found ${filteredBusinesses.length} businesses after filtering.`);
    return filteredBusinesses.slice(0, maxResults);
  }
  async extractUsingInjectedScript() {
    return new Promise((resolve) => {
      const messageHandler = (event) => {
        if (event.source !== window) return;
        if (event.data.type === "GMAPS_EXTRACT_RESPONSE") {
          window.removeEventListener("message", messageHandler);
          console.log("Received data from injected script:", event.data.data);
          resolve(event.data.data || []);
        }
      };
      window.addEventListener("message", messageHandler);
      window.postMessage({
        type: "GMAPS_EXTRACT_REQUEST"
      }, "*");
      setTimeout(() => {
        window.removeEventListener("message", messageHandler);
        console.log("Injected script extraction timed out");
        resolve([]);
      }, 5e3);
    });
  }
  async extractFromDOM(maxResults, searchParams) {
    const businesses = [];
    let processedBusinesses = /* @__PURE__ */ new Set();
    console.log(`Starting extraction with target of ${maxResults} businesses`);
    await this.waitForSearchResults();
    while (businesses.length < maxResults) {
      console.log(`Current progress: ${businesses.length}/${maxResults} businesses extracted`);
      const currentElements = this.findCurrentBusinessElements();
      if (currentElements.length === 0) {
        console.log("No business elements found, search may have failed");
        break;
      }
      console.log(`Found ${currentElements.length} business elements in current view`);
      let newBusinessesFound = false;
      for (let i = 0; i < currentElements.length && businesses.length < maxResults; i++) {
        const element = currentElements[i];
        const businessId = this.createBusinessIdentifier(element);
        if (processedBusinesses.has(businessId)) {
          console.log(`Skipping already processed business: ${businessId}`);
          continue;
        }
        console.log(`Processing business ${businesses.length + 1}/${maxResults}: ${businessId}`);
        processedBusinesses.add(businessId);
        try {
          const businessData = await this.processIndividualBusiness(element, businesses.length + 1, searchParams);
          if (businessData) {
            businesses.push(businessData);
            newBusinessesFound = true;
            console.log(`Successfully extracted: ${businessData.name}`);
            chrome.runtime.sendMessage({
              type: "EXTRACTION_PROGRESS",
              payload: {
                current: businesses.length,
                total: maxResults,
                status: "extracting",
                business: businessData.name
              }
            });
          }
          await this.delay(2e3);
        } catch (error) {
          console.error(`Error processing business ${i + 1}:`, error);
        }
      }
      if (businesses.length < maxResults) {
        console.log(`Need more businesses (${businesses.length}/${maxResults}), attempting to load more results...`);
        const moreResultsLoaded = await this.loadMoreSearchResults();
        if (!moreResultsLoaded && !newBusinessesFound) {
          console.log("No more results available, stopping extraction");
          break;
        }
        await this.delay(3e3);
      }
    }
    console.log(`Extraction completed. Found ${businesses.length} businesses.`);
    return businesses;
  }
  // Removed duplicate waitForSearchResults method - using the one defined earlier
  findCurrentBusinessElements() {
    const resultSelectors = [
      ".Nv2PK.THOPZb.CpccDe",
      // Main business result container (exact match from HTML)
      ".Nv2PK",
      // Fallback - main container
      ".bfdHYd.Ppzolf.OFBs3e",
      // Business content container
      "a.hfpxzc",
      // Business link container
      '[jsaction*="pane.wfvdle"]',
      // Interactive elements with specific actions
      'div[jsaction*="mouseover:pane"]',
      // Mouseover interactive elements
      "[data-result-index]",
      // Traditional result index
      '[role="article"]',
      // Article role elements
      'a[href*="/place/"]'
      // Place links
    ];
    for (const selector of resultSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        console.log(`Found ${elements.length} results with selector: ${selector}`);
        return Array.from(elements);
      }
    }
    return [];
  }
  createBusinessIdentifier(element) {
    const name = this.extractNameFromElement(element) || "";
    const address = this.extractAddressFromElement(element) || "";
    const link = element.querySelector("a.hfpxzc");
    const href = link?.href || "";
    if (href) {
      const placeMatch = href.match(/place\/([^\/]+)/);
      if (placeMatch) {
        return placeMatch[1];
      }
    }
    return `${name}-${address}`.replace(/\s+/g, "-").toLowerCase();
  }
  async processIndividualBusiness(element, businessIndex, searchParams) {
    try {
      console.log(`Processing business ${businessIndex}...`);
      const basicInfo = this.extractFromSearchResultElement(element);
      console.log(`Basic info extracted: ${basicInfo.name || "Unknown"}`);
      const detailedInfo = await this.clickAndExtractDetailedInfo(element, businessIndex - 1);
      let businessData = null;
      if (detailedInfo && detailedInfo.name) {
        console.log(`Successfully extracted detailed info: ${detailedInfo.name}`);
        businessData = {
          id: generateId(),
          name: detailedInfo.name || basicInfo.name || "Unknown Business",
          address: detailedInfo.address || basicInfo.address || "Address not available",
          category: detailedInfo.category || basicInfo.category || "Unknown",
          rating: detailedInfo.rating || basicInfo.rating,
          reviewCount: detailedInfo.reviewCount || basicInfo.reviewCount,
          phone: detailedInfo.phone,
          website: detailedInfo.website,
          email: detailedInfo.email,
          hours: detailedInfo.hours || basicInfo.hours || [],
          photos: searchParams.includePhotos ? detailedInfo.photos || [] : [],
          reviews: searchParams.includeReviews ? detailedInfo.reviews || [] : [],
          placeId: detailedInfo.placeId,
          extractedAt: /* @__PURE__ */ new Date(),
          source: "google_maps"
        };
      } else if (basicInfo && basicInfo.name) {
        console.log(`Detailed extraction failed, using basic info: ${basicInfo.name}`);
        businessData = {
          id: generateId(),
          name: basicInfo.name,
          address: basicInfo.address || "Address not available",
          category: basicInfo.category || "Unknown",
          rating: basicInfo.rating,
          reviewCount: basicInfo.reviewCount,
          phone: void 0,
          website: void 0,
          hours: basicInfo.hours || [],
          photos: [],
          reviews: [],
          extractedAt: /* @__PURE__ */ new Date(),
          source: "google_maps"
        };
      }
      return businessData;
    } catch (error) {
      console.error(`Error processing individual business:`, error);
      return null;
    }
  }
  async loadMoreSearchResults() {
    try {
      console.log("Attempting to load more search results by scrolling...");
      const scrollContainers = [
        ".m6QErb.DxyBCb.kA9KIf.dS8AEf",
        // Main results panel
        '[role="main"]',
        // Main content area
        ".siAUzd",
        // Results container
        ".section-listbox",
        // List container
        ".section-scrollbox"
        // Scroll container
      ];
      let scrollContainer = null;
      for (const selector of scrollContainers) {
        const container = document.querySelector(selector);
        if (container) {
          scrollContainer = container;
          console.log(`Found scroll container: ${selector}`);
          break;
        }
      }
      if (!scrollContainer) {
        console.log("No scroll container found, trying window scroll");
        scrollContainer = document.body;
      }
      const initialCount = this.findCurrentBusinessElements().length;
      console.log(`Initial business count: ${initialCount}`);
      const scrollSteps = 3;
      const scrollAmount = scrollContainer.scrollHeight / scrollSteps;
      for (let step = 1; step <= scrollSteps; step++) {
        console.log(`Scrolling step ${step}/${scrollSteps}...`);
        if (scrollContainer === document.body) {
          window.scrollTo(0, window.scrollY + scrollAmount);
        } else {
          scrollContainer.scrollTo(0, scrollContainer.scrollTop + scrollAmount);
        }
        await this.delay(2e3);
        const currentCount = this.findCurrentBusinessElements().length;
        if (currentCount > initialCount) {
          console.log(`New results loaded: ${currentCount} total (was ${initialCount})`);
          if (scrollContainer === document.body) {
            window.scrollTo(0, window.scrollY - scrollAmount / 2);
          } else {
            scrollContainer.scrollTo(0, scrollContainer.scrollTop - scrollAmount / 2);
          }
          await this.delay(1e3);
          return true;
        }
      }
      const finalCount = this.findCurrentBusinessElements().length;
      console.log(`Final business count after scrolling: ${finalCount}`);
      if (finalCount > initialCount) {
        console.log(`Successfully loaded ${finalCount - initialCount} more results`);
        return true;
      } else {
        console.log("No new results loaded after scrolling");
        return false;
      }
    } catch (error) {
      console.error("Error loading more search results:", error);
      return false;
    }
  }
  applyFilters(businesses, filterOptions) {
    return businesses.filter((business) => {
      if (business.rating) {
        const [minRating, maxRating] = filterOptions.ratingRange;
        if (business.rating < minRating || business.rating > maxRating) {
          return false;
        }
      }
      if (filterOptions.hasPhone && !business.phone) {
        return false;
      }
      if (filterOptions.hasWebsite && !business.website) {
        return false;
      }
      return true;
    });
  }
  async extractCurrentBusinessView(searchParams) {
    try {
      console.log("Extracting from current business view...");
      const name = this.extractBusinessName();
      const address = this.extractBusinessAddress();
      const category = this.extractBusinessCategory();
      const rating = this.extractBusinessRating();
      const reviewCount = this.extractBusinessReviewCount();
      const phone = this.extractBusinessPhone();
      const website = this.extractBusinessWebsite();
      const hours = this.extractBusinessHours();
      const photos = searchParams.includePhotos ? this.extractBusinessPhotos() : [];
      const reviews = searchParams.includeReviews ? await this.extractBusinessReviews() : [];
      if (!name) {
        console.log("No business name found in current view");
        return null;
      }
      const business = {
        id: generateId(),
        name,
        address: address || "Address not available",
        category: category || "Unknown",
        rating,
        reviewCount,
        phone,
        website,
        hours: hours || [],
        photos: photos || [],
        reviews: reviews || [],
        extractedAt: /* @__PURE__ */ new Date(),
        source: "google_maps"
      };
      console.log("Extracted business from current view:", business.name);
      return sanitizeBusinessData(business);
    } catch (error) {
      console.error("Failed to extract from current business view:", error);
      return null;
    }
  }
  async extractBusinessFromElement(element, searchParams) {
    try {
      console.log("Extracting business from element...");
      const directExtraction = this.extractFromSearchResultElement(element);
      if (directExtraction.name) {
        console.log("Successfully extracted from search result:", directExtraction.name);
        const business2 = {
          id: generateId(),
          name: directExtraction.name,
          address: directExtraction.address || "Address not available",
          category: directExtraction.category || "Unknown",
          rating: directExtraction.rating,
          reviewCount: directExtraction.reviewCount,
          phone: directExtraction.phone,
          website: directExtraction.website,
          hours: [],
          photos: [],
          reviews: [],
          extractedAt: /* @__PURE__ */ new Date(),
          source: "google_maps"
        };
        return sanitizeBusinessData(business2);
      }
      console.log("Clicking on business element for detailed info...");
      element.click();
      await this.delay(3e3);
      const name = this.extractBusinessName();
      const address = this.extractBusinessAddress();
      const category = this.extractBusinessCategory();
      const rating = this.extractBusinessRating();
      const reviewCount = this.extractBusinessReviewCount();
      const phone = this.extractBusinessPhone();
      const website = this.extractBusinessWebsite();
      const hours = this.extractBusinessHours();
      const photos = searchParams.includePhotos ? this.extractBusinessPhotos() : [];
      const reviews = searchParams.includeReviews ? await this.extractBusinessReviews() : [];
      console.log("Extracted data from detail view:", { name, address, category, rating, reviewCount, phone, website });
      if (!name) {
        console.log("No business name found, skipping...");
        return null;
      }
      const business = {
        id: generateId(),
        name,
        address: address || "Address not available",
        category: category || "Unknown",
        rating,
        reviewCount,
        phone,
        website,
        hours: hours || [],
        photos: photos || [],
        reviews: reviews || [],
        extractedAt: /* @__PURE__ */ new Date(),
        source: "google_maps"
      };
      return sanitizeBusinessData(business);
    } catch (error) {
      console.error("Failed to extract business data:", error);
      return null;
    }
  }
  extractFromSearchResultElement(element) {
    try {
      console.log("Extracting from search result element...");
      const dataFromAttributes = this.extractFromElementAttributes(element);
      if (dataFromAttributes.name) {
        console.log("Successfully extracted from attributes:", dataFromAttributes);
        return dataFromAttributes;
      }
      const name = this.extractNameFromElement(element);
      const address = this.extractAddressFromElement(element);
      const category = this.extractCategoryFromElement(element);
      const rating = this.extractRatingFromElement(element);
      const reviewCount = this.extractReviewCountFromElement(element);
      const hours = this.extractHoursFromElement(element);
      console.log("DOM extraction result (search list - abbreviated info):", { name, address, category, rating, reviewCount, hours });
      return { name, address, category, rating, reviewCount, hours };
    } catch (error) {
      console.error("Failed to extract from search result element:", error);
      return {};
    }
  }
  extractFromElementAttributes(element) {
    try {
      const ariaLabel = element.getAttribute("aria-label");
      if (ariaLabel) {
        const nameMatch = ariaLabel.match(/^([^,]+)/);
        const ratingMatch = ariaLabel.match(/(\d+\.?\d*)\s*stars?/i);
        const reviewMatch = ariaLabel.match(/(\d+)\s*reviews?/i);
        if (nameMatch) {
          return {
            name: nameMatch[1].trim(),
            rating: ratingMatch ? parseFloat(ratingMatch[1]) : void 0,
            reviewCount: reviewMatch ? parseInt(reviewMatch[1]) : void 0
          };
        }
      }
      const dataTitle = element.getAttribute("data-title") || element.getAttribute("title");
      if (dataTitle) {
        return { name: dataTitle };
      }
      return {};
    } catch (error) {
      return {};
    }
  }
  extractNameFromElement(element) {
    const selectors = [
      ".qBF1Pd.fontHeadlineSmall",
      // Exact match from HTML: business name
      ".qBF1Pd",
      // Fallback - business name container
      ".fontHeadlineSmall",
      // Headline text
      "h3",
      // Header fallback
      ".DUwDvf",
      // Another name selector
      "[data-value]",
      // Data value attribute
      ".fontHeadlineLarge"
      // Large headline
    ];
    for (const selector of selectors) {
      const nameEl = element.querySelector(selector);
      if (nameEl?.textContent?.trim()) {
        const name = nameEl.textContent.trim();
        if (name.length > 2 && name.length < 100 && !name.includes("Google")) {
          console.log(`Found name with selector ${selector}: ${name}`);
          return name;
        }
      }
    }
    const textContent = element.textContent?.trim();
    if (textContent) {
      const firstLine = textContent.split("\n")[0].trim();
      if (firstLine.length > 2 && firstLine.length < 100) {
        console.log(`Found name from text content: ${firstLine}`);
        return firstLine;
      }
    }
    return "";
  }
  extractAddressFromElement(element) {
    const selectors = [
      // Exact structure from HTML: look for address in nested spans, skip icons
      ".W4Efsd .W4Efsd span:last-child span",
      // Address in nested structure (last span)
      ".W4Efsd .W4Efsd span:last-child",
      // Address container
      ".W4Efsd span:last-child span",
      // Direct address span
      ".W4Efsd span:last-child",
      // Address container fallback
      // Alternative selectors
      ".Io6YTe",
      // Alternative address selector
      ".rogA2c",
      // Another address selector
      ".fontBodyMedium",
      // Body text
      ".AeaXub"
      // Address container
    ];
    for (const selector of selectors) {
      const addressEl = element.querySelector(selector);
      if (addressEl?.textContent?.trim()) {
        const text = addressEl.textContent.trim();
        if (this.looksLikeAddress(text)) {
          console.log(`Found address with selector ${selector}: ${text} (Note: This is abbreviated address from search results)`);
          return text + " (abbreviated)";
        }
      }
    }
    const w4Elements = element.querySelectorAll(".W4Efsd .W4Efsd span");
    for (let i = 0; i < w4Elements.length; i++) {
      const span = w4Elements[i];
      const text = span.textContent?.trim();
      if (text && !text.includes("·") && !span.querySelector(".google-symbols") && !this.looksLikeCategory(text) && this.looksLikeAddress(text)) {
        console.log(`Found address in nested W4Efsd span: ${text} (abbreviated)`);
        return text + " (abbreviated)";
      }
    }
    return "";
  }
  extractCategoryFromElement(element) {
    const selectors = [
      // Exact structure from HTML: W4Efsd > W4Efsd > span > span (first one is category)
      ".W4Efsd .W4Efsd span:first-child span",
      // Category in nested structure
      ".W4Efsd .W4Efsd span:first-child",
      // Category container
      ".W4Efsd span:first-child span",
      // Direct category span
      ".W4Efsd span:first-child",
      // Category container fallback
      // Alternative selectors
      ".DkEaL",
      // Alternative category selector
      ".mgr77e",
      // Business type selector
      ".fontBodySmall",
      // Small body text
      ".YhemCb"
      // Another category selector
    ];
    for (const selector of selectors) {
      const categoryEl = element.querySelector(selector);
      if (categoryEl?.textContent?.trim()) {
        const text = categoryEl.textContent.trim();
        if (this.looksLikeCategory(text)) {
          console.log(`Found category with selector ${selector}: ${text}`);
          return text;
        }
      }
    }
    const w4Elements = element.querySelectorAll(".W4Efsd .W4Efsd span");
    for (let i = 0; i < w4Elements.length; i++) {
      const span = w4Elements[i];
      const text = span.textContent?.trim();
      if (text && !text.includes("·") && !span.querySelector(".google-symbols") && this.looksLikeCategory(text)) {
        console.log(`Found category in nested W4Efsd span: ${text}`);
        return text;
      }
    }
    return "";
  }
  extractRatingFromElement(element) {
    const selectors = [
      '.MW4etd[aria-hidden="true"]',
      // Exact match from HTML: rating number
      ".MW4etd",
      // Fallback - rating container
      ".ceNzKf",
      // Alternative rating selector
      '[aria-label*="stars"]',
      // Aria label with stars
      'span[role="img"][aria-label*="stars"]'
      // Rating with role img
    ];
    for (const selector of selectors) {
      const ratingEl = element.querySelector(selector);
      if (ratingEl?.textContent?.trim()) {
        const ratingText = ratingEl.textContent.trim();
        const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
        if (ratingMatch) {
          const rating = parseFloat(ratingMatch[1]);
          if (rating >= 0 && rating <= 5) {
            console.log(`Found rating: ${rating}`);
            return rating;
          }
        }
      }
    }
    return void 0;
  }
  extractReviewCountFromElement(element) {
    const selectors = [
      '.UY7F9[aria-hidden="true"]',
      // Exact match from HTML: review count in parentheses
      ".UY7F9",
      // Fallback - review count container
      ".HHrUdb",
      // Alternative review count selector
      '[aria-label*="reviews"]',
      // Aria label with reviews
      'span[aria-label*="Reviews"]'
      // Review count with capital R
    ];
    for (const selector of selectors) {
      const reviewEl = element.querySelector(selector);
      if (reviewEl?.textContent?.trim()) {
        const reviewText = reviewEl.textContent.trim();
        const reviewMatch = reviewText.match(/\(?([0-9,]+)\)?/);
        if (reviewMatch) {
          const count = parseInt(reviewMatch[1].replace(/,/g, ""));
          if (count > 0) {
            console.log(`Found review count: ${count}`);
            return count;
          }
        }
      }
    }
    return void 0;
  }
  looksLikeAddress(text) {
    return text.length > 10 && !text.match(/^\d+\.\d+/) && !text.match(/^\d+[\d\s\-\(\)]+$/) && !text.includes("★") && !text.includes("•");
  }
  looksLikeCategory(text) {
    return text.length < 50 && text.length > 2 && !text.includes(",") && !text.match(/^\d/) && !text.includes("★") && !this.looksLikeAddress(text);
  }
  extractBusinessName() {
    const selectors = [
      // Business detail page selectors
      'h1[data-attrid="title"]',
      "h1.DUwDvf",
      "h1.x3AX1-LfntMc-header-title-title",
      '[data-attrid="title"] h1',
      ".x3AX1-LfntMc-header-title-title",
      // Search results selectors
      ".qBF1Pd",
      // Primary business name in search results
      ".fontHeadlineSmall",
      // Alternative headline
      ".fontHeadlineLarge",
      // Large headline
      ".section-result-title",
      // Result title
      ".section-result-title-container .section-result-title",
      // Newer selectors (2024-2025)
      '[data-value*="directions"] .qBF1Pd',
      ".hfpxzc .qBF1Pd",
      // Business name in list
      ".Nv2PK .qBF1Pd",
      // Another list container
      ".bfdHYd .qBF1Pd",
      // Alternative container
      // Fallback selectors
      '[data-value="title"]',
      "h1",
      // Last resort
      "h2",
      // Alternative header
      "h3"
      // Alternative header
    ];
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const name = element.textContent.trim();
        if (name.length > 1 && name.length < 200 && !name.includes("Google Maps")) {
          console.log(`Found business name with selector ${selector}: ${name}`);
          return name;
        }
      }
    }
    console.log("No business name found with any selector");
    return "";
  }
  extractBusinessAddress() {
    const selectors = [
      // Business detail page selectors
      '[data-attrid="kc:/location/location:address"]',
      ".QSFF4-text",
      '[data-value="Address"]',
      ".Io6YTe",
      // Primary address selector
      ".rogA2c",
      // Alternative address selector
      '[data-item-id="address"]',
      ".fontBodyMedium .Io6YTe",
      // Search results selectors
      ".W4Efsd:last-child",
      // Address in search results (last child)
      ".W4Efsd:nth-child(2)",
      // Address as second child
      ".W4Efsd:nth-child(3)",
      // Address as third child
      ".W4Efsd .Io6YTe",
      // Address within W4Efsd container
      // Newer selectors (2024-2025)
      ".hfpxzc .W4Efsd:nth-child(2)",
      // Address in business list
      ".Nv2PK .W4Efsd:nth-child(2)",
      // Address in alternative container
      ".bfdHYd .W4Efsd:nth-child(2)",
      // Address in another container
      ".section-result-location",
      // Location in results
      ".section-result-details .W4Efsd",
      // Details container
      // Additional selectors
      '[jsaction*="address"]',
      ".fontBodyMedium",
      // General body text
      ".AeaXub",
      // Address container
      ".LrzXr",
      // Another address class
      '.W4Efsd[data-value*="address"]'
      // Data attribute
    ];
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const text = element.textContent.trim();
        if (this.looksLikeAddress(text)) {
          console.log(`Found address with selector ${selector}: ${text}`);
          return text;
        }
      }
    }
    console.log("No address found with any selector");
    return "";
  }
  extractBusinessCategory() {
    const selectors = [
      // Business detail page selectors
      ".DkEaL",
      // Primary category selector
      '[data-attrid="kc:/location/location:category"]',
      ".fontBodyMedium .DkEaL",
      ".YhemCb",
      // Alternative category selector
      ".mgr77e",
      // Business type selector
      // Search results selectors
      ".W4Efsd:first-child",
      // Category as first child in search results
      ".W4Efsd:nth-child(1)",
      // Category as first child
      ".fontBodySmall",
      // Small text category
      ".section-result-category",
      // Category in results
      // Newer selectors (2024-2025)
      ".hfpxzc .W4Efsd:first-child",
      // Category in business list
      ".Nv2PK .W4Efsd:first-child",
      // Category in alternative container
      ".bfdHYd .W4Efsd:first-child",
      // Category in another container
      ".section-result-details .DkEaL",
      // Category in details
      // Additional selectors
      '[jsaction*="category"]',
      ".fontBodySmall .DkEaL",
      // Small category text
      '.W4Efsd[data-value*="category"]',
      // Data attribute
      ".business-category",
      // Generic category class
      ".place-category"
      // Place category
    ];
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const text = element.textContent.trim();
        if (this.looksLikeCategory(text)) {
          console.log(`Found category with selector ${selector}: ${text}`);
          return text;
        }
      }
    }
    console.log("No category found with any selector");
    return "";
  }
  extractBusinessRating() {
    const selectors = [
      ".ceNzKf",
      '[data-attrid="kc:/location/location:rating"]'
    ];
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const rating = parseFloat(element.textContent.trim());
        if (!isNaN(rating)) {
          return rating;
        }
      }
    }
    return void 0;
  }
  extractBusinessReviewCount() {
    const selectors = [
      ".HHrUdb",
      '[data-attrid="kc:/location/location:review_count"]'
    ];
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const text = element.textContent.trim();
        const match = text.match(/\(([0-9,]+)\)/);
        if (match) {
          return parseInt(match[1].replace(/,/g, ""));
        }
      }
    }
    return void 0;
  }
  extractBusinessPhone() {
    const selectors = [
      // Business detail page selectors
      '[data-attrid="kc:/location/location:phone"]',
      'a[href^="tel:"]',
      // Phone links
      '[data-value*="phone"]',
      '[data-item-id="phone"]',
      // Search results and detail selectors
      '.rogA2c[data-value*="phone"]',
      // Phone in details
      '.W4Efsd[data-value*="phone"]',
      // Phone in search results
      '.fontBodyMedium[data-value*="phone"]',
      // Phone in body text
      // Newer selectors (2024-2025)
      ".section-result-phone",
      // Phone in results
      ".place-phone",
      // Place phone
      ".business-phone",
      // Business phone
      '[jsaction*="phone"]',
      // Phone action
      // Pattern-based selectors (look for phone-like text)
      ".W4Efsd",
      // Check all W4Efsd for phone patterns
      ".fontBodyMedium",
      // Check body text for phone patterns
      ".Io6YTe"
      // Check address-like containers for phone
    ];
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const text = element.textContent?.trim();
        if (text && this.looksLikePhone(text)) {
          console.log(`Found phone with selector ${selector}: ${text}`);
          return text;
        }
        const href = element.getAttribute("href");
        if (href && href.startsWith("tel:")) {
          const phone = href.replace("tel:", "").trim();
          console.log(`Found phone from tel link: ${phone}`);
          return phone;
        }
      }
    }
    console.log("No phone found with any selector");
    return void 0;
  }
  extractBusinessWebsite() {
    const selectors = [
      // Business detail page selectors
      '[data-attrid="kc:/location/location:website"] a',
      'a[href^="http"]:not([href*="google"])',
      // External links
      '[data-value*="website"]',
      '[data-item-id="website"]',
      // Search results and detail selectors
      '.rogA2c[data-value*="website"]',
      // Website in details
      '.W4Efsd[data-value*="website"]',
      // Website in search results
      // Newer selectors (2024-2025)
      ".section-result-website",
      // Website in results
      ".place-website",
      // Place website
      ".business-website",
      // Business website
      '[jsaction*="website"]',
      // Website action
      // Link-based selectors
      'a[href*="://"]',
      // Any external link
      'a[target="_blank"]'
      // Links opening in new tab
    ];
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const href = element.getAttribute("href");
        if (href && this.looksLikeWebsite(href)) {
          console.log(`Found website with selector ${selector}: ${href}`);
          return href;
        }
        const text = element.textContent?.trim();
        if (text && this.looksLikeWebsite(text)) {
          console.log(`Found website from text: ${text}`);
          return text;
        }
      }
    }
    console.log("No website found with any selector");
    return void 0;
  }
  extractBusinessHours() {
    const selectors = [
      // Business detail page selectors
      '[data-attrid="kc:/location/location:hours"]',
      ".t39EBf",
      // Hours container
      ".OqCZI",
      // Hours table
      // Search results selectors - hours in W4Efsd containers
      '.W4Efsd span[style*="color"]',
      // Hours with color styling (Closed/Open)
      ".W4Efsd",
      // All W4Efsd containers that might contain hours
      // General selectors
      ".fontBodySmall",
      // Small body text that might contain hours
      ".hours-info",
      // Generic hours class
      '[data-value*="hours"]'
      // Data attribute with hours
    ];
    const hoursInfo = [];
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        if (element.classList.contains("W4Efsd")) {
          const fullText = element.textContent?.trim();
          if (fullText && (fullText.includes("Closed") || fullText.includes("Opens") || fullText.includes("AM") || fullText.includes("PM") || fullText.includes("⋅"))) {
            const hoursData = this.parseHoursText(fullText);
            if (hoursData && !hoursInfo.some((h) => h.text === hoursData.text)) {
              hoursInfo.push(hoursData);
              console.log(`Found hours in W4Efsd: ${fullText}`);
            }
          }
        } else {
          const text = element.textContent?.trim();
          if (text && this.looksLikeHours(text)) {
            const hoursData = this.parseHoursText(text);
            if (hoursData && !hoursInfo.some((h) => h.text === hoursData.text)) {
              hoursInfo.push(hoursData);
              console.log(`Found hours: ${text}`);
            }
          }
        }
      }
    }
    console.log(`Extracted ${hoursInfo.length} hours entries`);
    return hoursInfo;
  }
  // Removed duplicate methods - using the enhanced ones defined later in the file
  async extractReviews(params, sendResponse) {
    sendResponse({ success: true, data: [] });
  }
  async extractPhotos(params, sendResponse) {
    sendResponse({ success: true, data: [] });
  }
  async getCurrentBusiness(sendResponse) {
    sendResponse({ success: true, data: null });
  }
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  looksLikePhone(text) {
    const phonePatterns = [
      /^\+?[\d\s\-\(\)]{10,}$/,
      // General phone pattern
      /^\(\d{3}\)\s?\d{3}-?\d{4}$/,
      // (*************
      /^\d{3}-?\d{3}-?\d{4}$/,
      // ************
      /^\+\d{1,3}\s?\d{3,}/
      // International format
    ];
    return phonePatterns.some((pattern) => pattern.test(text.trim())) && text.length >= 10 && text.length <= 20;
  }
  looksLikeWebsite(url) {
    try {
      const urlObj = new URL(url);
      const excludedDomains = [
        "google.com",
        "maps.google.com",
        "gstatic.com",
        "googleapis.com",
        "googleusercontent.com"
      ];
      return !excludedDomains.some((domain) => urlObj.hostname.includes(domain)) && (urlObj.protocol === "http:" || urlObj.protocol === "https:");
    } catch {
      return /^https?:\/\/[^\s]+\.[^\s]+/.test(url) && !url.includes("google.com") && !url.includes("gstatic.com");
    }
  }
  looksLikeEmail(text) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(text.trim());
  }
  looksLikeHours(text) {
    const hoursPatterns = [
      /\d{1,2}:\d{2}\s?(AM|PM|am|pm)/,
      /\d{1,2}(AM|PM|am|pm)/,
      /(Open|Closed|Hours)/i,
      /\d{1,2}:\d{2}\s?-\s?\d{1,2}:\d{2}/,
      /Opens\s+\d{1,2}/i,
      // "Opens 10 AM"
      /⋅/
      // Contains bullet point (common in hours)
    ];
    return hoursPatterns.some((pattern) => pattern.test(text));
  }
  extractHoursFromElement(element) {
    const hoursSelectors = [
      '.W4Efsd span[style*="color"]',
      // Hours with color styling (Closed/Open)
      ".W4Efsd span",
      // All spans in W4Efsd
      ".fontBodySmall",
      // Small body text that might contain hours
      ".hours-info",
      // Generic hours class
      '[data-value*="hours"]'
      // Data attribute with hours
    ];
    const hoursInfo = [];
    for (const selector of hoursSelectors) {
      const elements = element.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const el = elements[i];
        const text = el.textContent?.trim();
        if (text && this.looksLikeHours(text)) {
          const hoursData = this.parseHoursText(text);
          if (hoursData) {
            hoursInfo.push(hoursData);
            console.log(`Found hours: ${text}`);
          }
        }
      }
    }
    const w4Elements = element.querySelectorAll(".W4Efsd");
    for (let i = 0; i < w4Elements.length; i++) {
      const w4Element = w4Elements[i];
      const fullText = w4Element.textContent?.trim();
      if (fullText && (fullText.includes("Closed") || fullText.includes("Opens") || fullText.includes("AM") || fullText.includes("PM"))) {
        const hoursData = this.parseHoursText(fullText);
        if (hoursData && !hoursInfo.some((h) => h.text === hoursData.text)) {
          hoursInfo.push(hoursData);
          console.log(`Found hours in W4Efsd: ${fullText}`);
        }
      }
    }
    return hoursInfo;
  }
  parseHoursText(text) {
    if (text.includes("Closed") && text.includes("Opens")) {
      const match = text.match(/Closed.*Opens\s+(\d{1,2}(?::\d{2})?\s*(?:AM|PM))/i);
      if (match) {
        return {
          status: "closed",
          opensAt: match[1],
          text
        };
      }
    } else if (text.includes("Open") && !text.includes("Opens")) {
      const match = text.match(/Open.*Closes\s+(\d{1,2}(?::\d{2})?\s*(?:AM|PM))/i);
      if (match) {
        return {
          status: "open",
          closesAt: match[1],
          text
        };
      }
    } else if (text.includes("24 hours") || text.includes("Open 24")) {
      return {
        status: "open_24_hours",
        text
      };
    }
    return null;
  }
  async clickAndExtractDetailedInfo(element, index) {
    try {
      console.log(`Clicking business ${index + 1} to extract detailed info...`);
      const businessLink = element.querySelector("a.hfpxzc");
      if (!businessLink) {
        console.log("No business link found for detailed extraction");
        return null;
      }
      const currentScrollY = window.scrollY;
      console.log("Clicking business link...");
      businessLink.click();
      console.log("Waiting for detail view to load...");
      const detailViewLoaded = await this.waitForDetailViewToLoad();
      if (!detailViewLoaded) {
        console.log("Detail view failed to load, skipping detailed extraction");
        return null;
      }
      console.log("Detail view loaded successfully");
      console.log("Extracting detailed info from opened business view...");
      const detailedInfo = await this.extractFromDetailView();
      await this.closeDetailView();
      await this.delay(2e3);
      window.scrollTo(0, currentScrollY);
      console.log("Detailed info extraction completed:", detailedInfo?.name || "No name");
      return detailedInfo;
    } catch (error) {
      console.error("Failed to extract detailed info by click:", error);
      try {
        await this.closeDetailView();
        await this.delay(1e3);
      } catch (closeError) {
        console.error("Failed to close detail view:", closeError);
      }
      return null;
    }
  }
  async closeDetailView() {
    try {
      console.log("Attempting to close detail view...");
      const closeButtonSelectors = [
        'button.VfPpkd-icon-LgbsSe.yHy1rc.eT1oJ.mN1ivc[aria-label="Close"]',
        // Exact business detail close button
        'button.VfPpkd-icon-LgbsSe[aria-label="Close"]',
        // Business detail close button
        '.hWERUb button[aria-label="Close"]',
        // Close button inside business detail container
        'button[aria-label="Close"].VfPpkd-icon-LgbsSe'
        // Alternative selector
      ];
      for (const selector of closeButtonSelectors) {
        const closeButton = document.querySelector(selector);
        if (closeButton && this.isElementVisible(closeButton)) {
          if (this.isSearchCloseButton(closeButton)) {
            console.log(`Skipping search close button with selector: ${selector}`);
            continue;
          }
          console.log(`Found and clicking business detail close button with selector: ${selector}`);
          closeButton.click();
          await this.delay(1e3);
          if (await this.waitForDetailViewToClose()) {
            console.log("Detail view closed successfully");
            return;
          }
        }
      }
      const backButton = document.querySelector('[data-value="back"]');
      if (backButton && this.isElementVisible(backButton)) {
        console.log("Clicking back button to close detail view");
        backButton.click();
        await this.delay(1e3);
        if (await this.waitForDetailViewToClose()) {
          console.log("Detail view closed via back button");
          return;
        }
      }
      console.log("Pressing Escape key to close detail view");
      document.dispatchEvent(new KeyboardEvent("keydown", {
        key: "Escape",
        code: "Escape",
        keyCode: 27,
        which: 27,
        bubbles: true
      }));
      await this.delay(1e3);
      if (await this.waitForDetailViewToClose()) {
        console.log("Detail view closed via Escape key");
        return;
      }
      const mapContainer = document.querySelector("#map");
      if (mapContainer) {
        console.log("Clicking map to close detail view");
        mapContainer.click();
        await this.delay(1e3);
      }
      console.log("Detail view close attempt completed");
    } catch (error) {
      console.error("Error closing detail view:", error);
    }
  }
  isElementVisible(element) {
    try {
      const rect = element.getBoundingClientRect();
      const style = window.getComputedStyle(element);
      return rect.width > 0 && rect.height > 0 && style.display !== "none" && style.visibility !== "hidden" && style.opacity !== "0";
    } catch (error) {
      return false;
    }
  }
  isSearchCloseButton(button) {
    try {
      if (button.classList.contains("yAuNSb") && button.classList.contains("vF7Cdb")) {
        console.log("Detected search close button by class names");
        return true;
      }
      if (button.getAttribute("jsaction")?.includes("omnibox.clear")) {
        console.log("Detected search close button by jsaction");
        return true;
      }
      const parent = button.closest(".lSDxNd");
      if (parent) {
        console.log("Detected search close button by parent structure");
        return true;
      }
      const businessDetailParent = button.closest(".hWERUb");
      if (businessDetailParent && button.classList.contains("VfPpkd-icon-LgbsSe")) {
        console.log("Confirmed business detail close button");
        return false;
      }
      return false;
    } catch (error) {
      console.error("Error checking if search close button:", error);
      return false;
    }
  }
  async waitForDetailViewToLoad(maxWaitTime = 1e4) {
    console.log("Waiting for detail view to load...");
    const startTime = Date.now();
    while (Date.now() - startTime < maxWaitTime) {
      const detailViewIndicators = [
        'button.VfPpkd-icon-LgbsSe[aria-label="Close"]',
        // Business detail close button
        '.hWERUb button[aria-label="Close"]',
        // Close button in business detail container
        ".m6QErb.DxyBCb.kA9KIf.dS8AEf",
        // Detail panel
        '[data-value="website"]',
        // Website link (usually only in detail view)
        '[data-value="phone"]',
        // Phone link (usually only in detail view)
        ".rogA2c",
        // Contact info container
        ".Io6YTe"
        // Address container in detail view
      ];
      let detailViewLoaded = false;
      for (const selector of detailViewIndicators) {
        const element = document.querySelector(selector);
        if (element && this.isElementVisible(element)) {
          detailViewLoaded = true;
          console.log(`Detail view loaded - found indicator: ${selector}`);
          break;
        }
      }
      if (detailViewLoaded) {
        await this.delay(1e3);
        console.log("Detail view confirmed loaded and stabilized");
        return true;
      }
      await this.delay(200);
    }
    console.log("Detail view failed to load within timeout");
    return false;
  }
  async waitForDetailViewToClose(maxWaitTime = 5e3) {
    const startTime = Date.now();
    while (Date.now() - startTime < maxWaitTime) {
      const detailViewIndicators = [
        'button.VfPpkd-icon-LgbsSe[aria-label="Close"]',
        // Business detail close button
        '.hWERUb button[aria-label="Close"]',
        // Close button in business detail container
        ".m6QErb.DxyBCb.kA9KIf.dS8AEf",
        // Detail panel
        '[data-value="website"]',
        // Website link (usually only in detail view)
        '[data-value="phone"]',
        // Phone link (usually only in detail view)
        ".rogA2c"
        // Contact info container in detail view
      ];
      let detailViewOpen = false;
      for (const selector of detailViewIndicators) {
        const element = document.querySelector(selector);
        if (element && this.isElementVisible(element)) {
          detailViewOpen = true;
          break;
        }
      }
      if (!detailViewOpen) {
        console.log("Detail view confirmed closed");
        return true;
      }
      await this.delay(200);
    }
    console.log("Detail view may still be open after timeout");
    return false;
  }
  async extractFromDetailView() {
    try {
      console.log("Extracting detailed information from business detail view...");
      await this.waitForContentToLoad();
      console.log("Extracting basic business information...");
      const name = await this.extractWithRetry(() => this.extractBusinessName(), "business name");
      const address = await this.extractWithRetry(() => this.extractBusinessAddress(), "address");
      const category = await this.extractWithRetry(() => this.extractBusinessCategory(), "category");
      const rating = await this.extractWithRetry(() => this.extractBusinessRating(), "rating");
      const reviewCount = await this.extractWithRetry(() => this.extractBusinessReviewCount(), "review count");
      console.log("Extracting contact information...");
      const phone = await this.extractWithRetry(() => this.extractBusinessPhone(), "phone");
      const website = await this.extractWithRetry(() => this.extractBusinessWebsite(), "website");
      const hours = await this.extractWithRetry(() => this.extractBusinessHours(), "hours");
      const additionalInfo = await this.extractAdditionalBusinessInfo();
      const placeId = this.extractPlaceIdFromCurrentUrl();
      const detailedInfo = {
        name,
        address,
        category,
        rating,
        reviewCount,
        phone,
        website,
        hours,
        placeId,
        email: additionalInfo.email,
        socialLinks: additionalInfo.socialLinks,
        bookingLink: additionalInfo.bookingLink,
        menuLink: additionalInfo.menuLink,
        shareLink: additionalInfo.shareLink,
        photos: additionalInfo.photos,
        reviews: additionalInfo.reviews
      };
      console.log("Detailed extraction completed:", {
        name: detailedInfo.name,
        address: detailedInfo.address,
        phone: detailedInfo.phone,
        website: detailedInfo.website,
        hasHours: detailedInfo.hours?.length > 0,
        hasPhotos: detailedInfo.photos?.length > 0
      });
      return detailedInfo;
    } catch (error) {
      console.error("Failed to extract from detail view:", error);
      return null;
    }
  }
  async extractAdditionalBusinessInfo() {
    try {
      console.log("Extracting additional business information...");
      const additionalInfo = {
        email: void 0,
        socialLinks: [],
        bookingLink: void 0,
        menuLink: void 0,
        shareLink: void 0,
        photos: [],
        reviews: []
      };
      await this.scrollToLoadContent();
      additionalInfo.email = this.extractBusinessEmail();
      additionalInfo.socialLinks = this.extractSocialLinks();
      additionalInfo.bookingLink = this.extractBookingLink();
      additionalInfo.menuLink = this.extractMenuLink();
      additionalInfo.shareLink = this.extractShareLink();
      additionalInfo.photos = this.extractBusinessPhotos();
      additionalInfo.reviews = this.extractBusinessReviews();
      return additionalInfo;
    } catch (error) {
      console.error("Failed to extract additional business info:", error);
      return {
        email: void 0,
        socialLinks: [],
        bookingLink: void 0,
        menuLink: void 0,
        shareLink: void 0,
        photos: [],
        reviews: []
      };
    }
  }
  async scrollToLoadContent() {
    try {
      console.log("Scrolling to load additional content...");
      const scrollContainer = document.querySelector('[role="main"]') || document.querySelector(".m6QErb") || document.querySelector(".siAUzd") || document.body;
      if (scrollContainer) {
        const scrollHeight = scrollContainer.scrollHeight;
        const steps = 3;
        const stepSize = scrollHeight / steps;
        for (let i = 1; i <= steps; i++) {
          scrollContainer.scrollTo(0, stepSize * i);
          await this.delay(1e3);
        }
        scrollContainer.scrollTo(0, 0);
        await this.delay(500);
      }
    } catch (error) {
      console.error("Error scrolling to load content:", error);
    }
  }
  extractPlaceIdFromCurrentUrl() {
    try {
      const url = window.location.href;
      return this.extractPlaceIdFromUrl(url);
    } catch (error) {
      console.error("Failed to extract place ID from current URL:", error);
      return void 0;
    }
  }
  extractPlaceIdFromUrl(url) {
    try {
      const placeIdMatch = url.match(/!1s([^!]+)/);
      if (placeIdMatch) {
        return placeIdMatch[1];
      }
      const altMatch = url.match(/\/place\/[^\/]+\/@[^\/]+\/data=([^&]+)/);
      if (altMatch) {
        return altMatch[1];
      }
      const dataMatch = url.match(/data=([^&]+)/);
      if (dataMatch) {
        return dataMatch[1];
      }
      return void 0;
    } catch (error) {
      console.error("Failed to extract place ID from URL:", error);
      return void 0;
    }
  }
  extractBusinessEmail() {
    const selectors = [
      'a[href^="mailto:"]',
      '[data-value*="email"]',
      '.rogA2c[data-value*="email"]'
    ];
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const href = element.getAttribute("href");
        if (href && href.startsWith("mailto:")) {
          const email = href.replace("mailto:", "").trim();
          if (this.looksLikeEmail(email)) {
            console.log(`Found email: ${email}`);
            return email;
          }
        }
        const text = element.textContent?.trim();
        if (text && this.looksLikeEmail(text)) {
          console.log(`Found email from text: ${text}`);
          return text;
        }
      }
    }
    return void 0;
  }
  extractSocialLinks() {
    const socialLinks = [];
    const socialPatterns = [
      /facebook\.com/i,
      /instagram\.com/i,
      /twitter\.com/i,
      /linkedin\.com/i,
      /youtube\.com/i,
      /tiktok\.com/i,
      /yelp\.com/i
    ];
    const links = document.querySelectorAll('a[href^="http"]');
    for (let i = 0; i < links.length; i++) {
      const link = links[i];
      const href = link.href;
      if (socialPatterns.some((pattern) => pattern.test(href))) {
        if (!socialLinks.includes(href)) {
          socialLinks.push(href);
          console.log(`Found social link: ${href}`);
        }
      }
    }
    return socialLinks;
  }
  extractBookingLink() {
    const selectors = [
      'a[href*="booking"]',
      'a[href*="reservation"]',
      'a[href*="appointment"]',
      'a[href*="book"]',
      '[data-value*="booking"]',
      '[aria-label*="book"]',
      '[aria-label*="reservation"]'
    ];
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.href) {
        console.log(`Found booking link: ${element.href}`);
        return element.href;
      }
    }
    return void 0;
  }
  extractMenuLink() {
    const selectors = [
      'a[href*="menu"]',
      'a[href*="food"]',
      '[data-value*="menu"]',
      '[aria-label*="menu"]',
      'a[href*="doordash"]',
      'a[href*="ubereats"]',
      'a[href*="grubhub"]'
    ];
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.href) {
        console.log(`Found menu link: ${element.href}`);
        return element.href;
      }
    }
    return void 0;
  }
  extractShareLink() {
    try {
      const currentUrl = window.location.href;
      if (currentUrl.includes("/place/")) {
        console.log(`Found share link: ${currentUrl}`);
        return currentUrl;
      }
      const shareButton = document.querySelector('[data-value="share"]');
      if (shareButton) {
        return currentUrl;
      }
      return void 0;
    } catch (error) {
      console.error("Failed to extract share link:", error);
      return void 0;
    }
  }
  extractBusinessPhotos() {
    const photos = [];
    try {
      const photoSelectors = [
        'img[src*="googleusercontent"]',
        'img[src*="maps.gstatic.com"]',
        ".section-photo img",
        "[data-photo-index] img"
      ];
      for (const selector of photoSelectors) {
        const images = document.querySelectorAll(selector);
        for (let i = 0; i < Math.min(images.length, 10); i++) {
          const img = images[i];
          if (img.src && !photos.includes(img.src)) {
            photos.push(img.src);
          }
        }
      }
      console.log(`Found ${photos.length} photos`);
    } catch (error) {
      console.error("Failed to extract photos:", error);
    }
    return photos;
  }
  extractBusinessReviews() {
    const reviews = [];
    try {
      const reviewElements = document.querySelectorAll(".jftiEf, .MyEned, .wiI7pd");
      for (let i = 0; i < Math.min(reviewElements.length, 5); i++) {
        const reviewEl = reviewElements[i];
        const author = reviewEl.querySelector(".d4r55")?.textContent?.trim();
        const rating = reviewEl.querySelector(".kvMYJc")?.getAttribute("aria-label");
        const text = reviewEl.querySelector(".wiI7pd")?.textContent?.trim();
        const date = reviewEl.querySelector(".rsqaWe")?.textContent?.trim();
        if (author && text) {
          reviews.push({
            author,
            rating: rating ? this.parseRatingFromText(rating) : void 0,
            text,
            date
          });
        }
      }
      console.log(`Found ${reviews.length} reviews`);
    } catch (error) {
      console.error("Failed to extract reviews:", error);
    }
    return reviews;
  }
  parseRatingFromText(text) {
    const match = text.match(/(\d+)/);
    return match ? parseInt(match[1]) : void 0;
  }
  async waitForContentToLoad() {
    console.log("Waiting for content to fully load...");
    const contentIndicators = [
      ".DUwDvf",
      // Business name
      ".Io6YTe",
      // Address
      ".rogA2c"
      // Contact info
    ];
    const maxWaitTime = 5e3;
    const startTime = Date.now();
    while (Date.now() - startTime < maxWaitTime) {
      let contentLoaded = false;
      for (const selector of contentIndicators) {
        const element = document.querySelector(selector);
        if (element && element.textContent?.trim()) {
          contentLoaded = true;
          break;
        }
      }
      if (contentLoaded) {
        console.log("Content indicators found, waiting for stabilization...");
        await this.delay(1e3);
        return;
      }
      await this.delay(200);
    }
    console.log("Content load timeout, proceeding with extraction...");
  }
  async extractWithRetry(extractFn, dataType, maxRetries = 3, retryDelay = 500) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = extractFn();
        if (result !== void 0 && result !== null && result !== "") {
          console.log(`Successfully extracted ${dataType} on attempt ${attempt}`);
          return result;
        }
        if (attempt < maxRetries) {
          console.log(`${dataType} not found on attempt ${attempt}, retrying...`);
          await this.delay(retryDelay);
        }
      } catch (error) {
        console.error(`Error extracting ${dataType} on attempt ${attempt}:`, error);
        if (attempt < maxRetries) {
          await this.delay(retryDelay);
        }
      }
    }
    console.log(`Failed to extract ${dataType} after ${maxRetries} attempts`);
    return extractFn();
  }
}
(function() {
  if (window.GoogleMapsExtractorInitialized) {
    console.log("Google Maps Extractor already initialized");
    return;
  }
  window.GoogleMapsExtractorInitialized = true;
  new GoogleMapsExtractor();
})();
