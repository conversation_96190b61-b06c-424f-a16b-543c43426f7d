// Injected script for Google Maps Extractor
// This script runs in the page context to access Google Maps internal APIs

// Type definitions for our injected functions
interface BusinessResult {
  index: number;
  name: string;
  address: string;
  rating: string;
  category: string;
  element: HTMLElement;
}

interface BusinessDetails {
  name?: string;
  address?: string;
  phone?: string;
  website?: string;
  rating?: number;
  reviewCount?: number;
  category?: string;
}

interface GMapsExtractorAPI {
  ready: boolean;
  getSearchResults(): BusinessResult[];
  performSearch(query: string): Promise<boolean>;
  waitForResults(timeout?: number): Promise<NodeListOf<Element>>;
  extractBusinessDetails(): BusinessDetails;
}

// Extend Window interface
declare global {
  interface Window {
    GMapsExtractor: GMapsExtractorAPI;
  }
}

(function() {
  'use strict';

  // Create a namespace for our injected functions
  window.GMapsExtractor = window.GMapsExtractor || {} as GMapsExtractorAPI;

  // Helper function to wait for elements
  function waitForElement(selector: string, timeout: number = 10000): Promise<Element> {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  // Helper function to simulate user input
  function simulateInput(element: HTMLInputElement, value: string): void {
    element.focus();
    element.value = value;
    
    // Trigger input events
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
  }

  // Helper function to simulate click
  function simulateClick(element: HTMLElement): void {
    const rect = element.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;

    element.dispatchEvent(new MouseEvent('mousedown', {
      bubbles: true,
      cancelable: true,
      clientX: x,
      clientY: y
    }));

    element.dispatchEvent(new MouseEvent('mouseup', {
      bubbles: true,
      cancelable: true,
      clientX: x,
      clientY: y
    }));

    element.dispatchEvent(new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      clientX: x,
      clientY: y
    }));
  }

  // Function to get search results
  window.GMapsExtractor.getSearchResults = function(): BusinessResult[] {
    const results: BusinessResult[] = [];
    const resultElements = document.querySelectorAll('[data-result-index]');
    
    resultElements.forEach((element, index) => {
      try {
        const nameElement = element.querySelector('[data-attrid="title"]') || 
                           element.querySelector('.qBF1Pd') ||
                           element.querySelector('h3');
        
        const addressElement = element.querySelector('[data-attrid="kc:/location/location:address"]') ||
                              element.querySelector('.W4Efsd:last-child .W4Efsd') ||
                              element.querySelector('.W4Efsd');

        const ratingElement = element.querySelector('.MW4etd') ||
                             element.querySelector('[data-attrid="kc:/location/location:rating"]');

        const categoryElement = element.querySelector('.W4Efsd:first-child') ||
                               element.querySelector('.DkEaL');

        if (nameElement) {
          results.push({
            index,
            name: nameElement.textContent?.trim() || '',
            address: addressElement?.textContent?.trim() || '',
            rating: ratingElement?.textContent?.trim() || '',
            category: categoryElement?.textContent?.trim() || '',
            element: element as HTMLElement
          });
        }
      } catch (error) {
        console.error('Error extracting result:', error);
      }
    });

    return results;
  };

  // Function to perform search
  window.GMapsExtractor.performSearch = async function(query: string): Promise<boolean> {
    try {
      const searchInput = await waitForElement('#searchboxinput') as HTMLInputElement;
      simulateInput(searchInput, query);
      
      // Wait a bit for autocomplete
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Try to click search button or press Enter
      const searchButton = document.querySelector('#searchbox-searchbutton') as HTMLElement;
      if (searchButton) {
        simulateClick(searchButton);
      } else {
        searchInput.dispatchEvent(new KeyboardEvent('keydown', {
          key: 'Enter',
          bubbles: true
        }));
      }

      return true;
    } catch (error) {
      console.error('Search failed:', error);
      return false;
    }
  };

  // Function to wait for search results
  window.GMapsExtractor.waitForResults = function(timeout: number = 10000): Promise<NodeListOf<Element>> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkResults = () => {
        const results = document.querySelectorAll('[data-result-index]');
        if (results.length > 0) {
          resolve(results);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error('Search results timeout'));
          return;
        }
        
        setTimeout(checkResults, 500);
      };
      
      checkResults();
    });
  };

  // Function to extract business details from current view
  window.GMapsExtractor.extractBusinessDetails = function(): BusinessDetails {
    const details: BusinessDetails = {};
    
    try {
      // Name
      const nameSelectors = [
        'h1[data-attrid="title"]',
        'h1.x3AX1-LfntMc-header-title-title',
        '[data-attrid="title"] h1',
        '.x3AX1-LfntMc-header-title-title'
      ];
      
      for (const selector of nameSelectors) {
        const element = document.querySelector(selector);
        if (element?.textContent?.trim()) {
          details.name = element.textContent.trim();
          break;
        }
      }

      // Address
      const addressSelectors = [
        '[data-attrid="kc:/location/location:address"]',
        '.QSFF4-text',
        '[data-value="Address"]'
      ];
      
      for (const selector of addressSelectors) {
        const element = document.querySelector(selector);
        if (element?.textContent?.trim()) {
          details.address = element.textContent.trim();
          break;
        }
      }

      // Phone
      const phoneElement = document.querySelector('[data-attrid="kc:/location/location:phone"]') ||
                          document.querySelector('a[href^="tel:"]');
      if (phoneElement?.textContent?.trim()) {
        details.phone = phoneElement.textContent.trim();
      }

      // Website
      const websiteElement = document.querySelector('[data-attrid="kc:/location/location:website"] a') ||
                            document.querySelector('a[href^="http"]:not([href*="google"])') as HTMLAnchorElement;
      if (websiteElement?.href) {
        details.website = websiteElement.href;
      }

      // Rating
      const ratingElement = document.querySelector('.ceNzKf') ||
                           document.querySelector('[data-attrid="kc:/location/location:rating"]');
      if (ratingElement?.textContent?.trim()) {
        const rating = parseFloat(ratingElement.textContent.trim());
        if (!isNaN(rating)) {
          details.rating = rating;
        }
      }

      // Review count
      const reviewElement = document.querySelector('.HHrUdb') ||
                           document.querySelector('[data-attrid="kc:/location/location:review_count"]');
      if (reviewElement?.textContent?.trim()) {
        const text = reviewElement.textContent.trim();
        const match = text.match(/\(([0-9,]+)\)/);
        if (match) {
          details.reviewCount = parseInt(match[1].replace(/,/g, ''));
        }
      }

      // Category
      const categoryElement = document.querySelector('.DkEaL') ||
                             document.querySelector('[data-attrid="kc:/location/location:category"]');
      if (categoryElement?.textContent?.trim()) {
        details.category = categoryElement.textContent.trim();
      }

    } catch (error) {
      console.error('Error extracting business details:', error);
    }

    return details;
  };

  // Signal that the injected script is ready
  window.GMapsExtractor.ready = true;
  
  console.log('Google Maps Extractor injected script loaded');

})();
