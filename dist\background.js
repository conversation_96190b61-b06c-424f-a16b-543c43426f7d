import { g as ChromeNotifications, e as ChromeStorage, h as DataValidator } from './dataProcessing.js';

class BackgroundService {
  extractionInProgress = false;
  dailyUsageCount = 0;
  monthlyUsageCount = 0;
  lastResetDate = (/* @__PURE__ */ new Date()).toDateString();
  constructor() {
    this.init();
  }
  init() {
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });
    this.setupPeriodicTasks();
    this.loadInitialData();
    console.log("Google Maps Extractor background service loaded");
  }
  async handleInstallation(details) {
    if (details.reason === "install") {
      await this.initializeFirstTimeUser();
      await ChromeNotifications.create(
        "Welcome to G Maps Extractor Pro!",
        "Click the extension icon to start extracting business data from Google Maps.",
        "basic"
      );
      chrome.tabs.create({
        url: chrome.runtime.getURL("sidebar.html?welcome=true")
      });
    } else if (details.reason === "update") {
      await ChromeNotifications.create(
        "G Maps Extractor Pro Updated",
        "Extension has been updated to the latest version.",
        "basic"
      );
    }
  }
  async initializeFirstTimeUser() {
    const defaultUser = {
      id: "user-" + Date.now(),
      email: "",
      name: "Free User",
      license: {
        type: "free",
        status: "active",
        limits: {
          dailyExtractions: 20,
          monthlyExtractions: 500,
          maxResults: 20,
          exportFormats: ["csv"],
          advancedFilters: false,
          bulkExtraction: false
        },
        usage: {
          dailyUsed: 0,
          monthlyUsed: 0,
          lastReset: /* @__PURE__ */ new Date()
        }
      },
      settings: {
        autoSave: true,
        defaultExportFormat: "csv",
        defaultFields: ["name", "address", "phone", "website", "rating"],
        notifications: true,
        theme: "light"
      },
      createdAt: /* @__PURE__ */ new Date()
    };
    await ChromeStorage.saveUser(defaultUser);
  }
  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case "BUSINESSES_EXTRACTED":
          await this.handleBusinessesExtracted(message.payload, sendResponse);
          break;
        case "VALIDATE_LICENSE":
          await this.validateLicense(message.payload, sendResponse);
          break;
        case "UPDATE_USAGE":
          await this.updateUsage(message.payload, sendResponse);
          break;
        case "GET_USER_DATA":
          await this.getUserData(sendResponse);
          break;
        case "EXPORT_DATA":
          await this.handleExportData(message.payload, sendResponse);
          break;
        case "CLEAR_DATA":
          await this.clearAllData(sendResponse);
          break;
        case "SYNC_DATA":
          await this.syncData(sendResponse);
          break;
        default:
          sendResponse({ success: false, error: "Unknown message type" });
      }
    } catch (error) {
      console.error("Background script error:", error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Unknown error" });
    }
  }
  async handleBusinessesExtracted(businesses, sendResponse) {
    try {
      const validBusinesses = businesses.map((business) => DataValidator.sanitizeBusinessData(business)).filter((business) => business !== null);
      if (validBusinesses.length === 0) {
        sendResponse({ success: false, error: "No valid business data received" });
        return;
      }
      const user = await ChromeStorage.getUser();
      if (user && !this.checkUsageLimits(user, validBusinesses.length)) {
        sendResponse({ success: false, error: "Usage limit exceeded. Please upgrade to Pro." });
        return;
      }
      const existingBusinesses = await ChromeStorage.getBusinessData();
      const allBusinesses = [...existingBusinesses, ...validBusinesses];
      await ChromeStorage.saveBusinessData(allBusinesses);
      if (user) {
        await this.updateUsageStats(user, validBusinesses.length);
      }
      await ChromeNotifications.create(
        "Extraction Complete",
        `Successfully extracted ${validBusinesses.length} businesses from Google Maps.`,
        "basic"
      );
      sendResponse({ success: true, data: validBusinesses });
    } catch (error) {
      console.error("Failed to handle extracted businesses:", error);
      sendResponse({ success: false, error: "Failed to save extracted data" });
    }
  }
  checkUsageLimits(user, newExtractions) {
    const license = user.license;
    this.resetUsageCountersIfNeeded(license);
    if (license.usage.dailyUsed + newExtractions > license.limits.dailyExtractions) {
      return false;
    }
    if (license.usage.monthlyUsed + newExtractions > license.limits.monthlyExtractions) {
      return false;
    }
    return true;
  }
  resetUsageCountersIfNeeded(license) {
    const now = /* @__PURE__ */ new Date();
    const lastReset = new Date(license.usage.lastReset);
    if (now.toDateString() !== lastReset.toDateString()) {
      license.usage.dailyUsed = 0;
    }
    if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
      license.usage.monthlyUsed = 0;
    }
    license.usage.lastReset = now;
  }
  async updateUsageStats(user, extractionCount) {
    user.license.usage.dailyUsed += extractionCount;
    user.license.usage.monthlyUsed += extractionCount;
    user.license.usage.lastReset = /* @__PURE__ */ new Date();
    await ChromeStorage.saveUser(user);
  }
  async validateLicense(licenseKey, sendResponse) {
    try {
      const validLicenseKeys = [
        "GMAPS-PRO-2024-DEMO",
        "GMAPS-ENTERPRISE-2024",
        "GMAPS-TRIAL-30DAYS"
      ];
      if (!validLicenseKeys.includes(licenseKey)) {
        sendResponse({ success: false, error: "Invalid license key" });
        return;
      }
      const proLicense = {
        type: licenseKey.includes("ENTERPRISE") ? "enterprise" : "pro",
        status: "active",
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1e3),
        // 1 year
        limits: {
          dailyExtractions: licenseKey.includes("ENTERPRISE") ? 1e4 : 1e3,
          monthlyExtractions: licenseKey.includes("ENTERPRISE") ? 1e5 : 25e3,
          maxResults: licenseKey.includes("ENTERPRISE") ? 1e3 : 500,
          exportFormats: ["csv", "excel", "json"],
          advancedFilters: true,
          bulkExtraction: true
        },
        usage: {
          dailyUsed: 0,
          monthlyUsed: 0,
          lastReset: /* @__PURE__ */ new Date()
        }
      };
      const user = await ChromeStorage.getUser();
      if (user) {
        user.license = proLicense;
        await ChromeStorage.saveUser(user);
      }
      sendResponse({ success: true, data: proLicense });
    } catch (error) {
      console.error("License validation failed:", error);
      sendResponse({ success: false, error: "License validation failed" });
    }
  }
  async getUserData(sendResponse) {
    try {
      const user = await ChromeStorage.getUser();
      sendResponse({ success: true, data: user });
    } catch (error) {
      console.error("Failed to get user data:", error);
      sendResponse({ success: false, error: "Failed to get user data" });
    }
  }
  async updateUsage(data, sendResponse) {
    try {
      const user = await ChromeStorage.getUser();
      if (user) {
        await this.updateUsageStats(user, data.count || 1);
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: "User not found" });
      }
    } catch (error) {
      console.error("Failed to update usage:", error);
      sendResponse({ success: false, error: "Failed to update usage" });
    }
  }
  async handleExportData(options, sendResponse) {
    try {
      const businesses = await ChromeStorage.getBusinessData();
      if (businesses.length === 0) {
        sendResponse({ success: false, error: "No data to export" });
        return;
      }
      const user = await ChromeStorage.getUser();
      if (user && !user.license.limits.exportFormats.includes(options.format)) {
        sendResponse({ success: false, error: "Export format not available in your license" });
        return;
      }
      sendResponse({ success: true, data: businesses });
    } catch (error) {
      console.error("Export failed:", error);
      sendResponse({ success: false, error: "Export failed" });
    }
  }
  async clearAllData(sendResponse) {
    try {
      await ChromeStorage.remove("extractedData");
      sendResponse({ success: true });
    } catch (error) {
      console.error("Failed to clear data:", error);
      sendResponse({ success: false, error: "Failed to clear data" });
    }
  }
  async syncData(sendResponse) {
    try {
      const user = await ChromeStorage.getUser();
      const businesses = await ChromeStorage.getBusinessData();
      sendResponse({
        success: true,
        data: {
          user,
          businesses,
          lastSync: (/* @__PURE__ */ new Date()).toISOString()
        }
      });
    } catch (error) {
      console.error("Sync failed:", error);
      sendResponse({ success: false, error: "Sync failed" });
    }
  }
  setupPeriodicTasks() {
    chrome.alarms.create("dailyReset", {
      delayInMinutes: this.getMinutesUntilMidnight(),
      periodInMinutes: 24 * 60
      // 24 hours
    });
    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === "dailyReset") {
        this.resetDailyUsage();
      }
    });
  }
  getMinutesUntilMidnight() {
    const now = /* @__PURE__ */ new Date();
    const midnight = /* @__PURE__ */ new Date();
    midnight.setHours(24, 0, 0, 0);
    return Math.floor((midnight.getTime() - now.getTime()) / (1e3 * 60));
  }
  async resetDailyUsage() {
    try {
      const user = await ChromeStorage.getUser();
      if (user) {
        user.license.usage.dailyUsed = 0;
        user.license.usage.lastReset = /* @__PURE__ */ new Date();
        await ChromeStorage.saveUser(user);
      }
    } catch (error) {
      console.error("Failed to reset daily usage:", error);
    }
  }
  async loadInitialData() {
    try {
      const user = await ChromeStorage.getUser();
      if (!user) {
        await this.initializeFirstTimeUser();
      }
    } catch (error) {
      console.error("Failed to load initial data:", error);
    }
  }
}
new BackgroundService();
