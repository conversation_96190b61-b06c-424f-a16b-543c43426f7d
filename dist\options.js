import { d as createSvgIcon, j as jsxRuntimeExports, R as ReactDOM, r as reactExports, T as ThemeProvider, l as lightTheme, C as CssBaseline, b as Container, B as Box, c as Typography, S as SettingsIcon, A as <PERSON><PERSON>, e as Card, f as CardContent, G as Grid, F as FormControlLabel, g as Switch, D as DownloadIcon, h as TextField, i as FormControl, I as InputLabel, k as Select, M as MenuItem, m as SecurityIcon, n as <PERSON><PERSON>, o as SaveIcon, p as Dialog, q as DialogTitle, s as DialogContent, t as DialogActions } from './index.js';

const NotificationsIcon = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"
}));

const RestoreIcon = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"
}));

const UploadIcon = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M5 20h14v-2H5zm0-10h4v6h6v-6h4l-7-7z"
}));

const defaultSettings = {
  autoSave: true,
  notifications: true,
  defaultExportFormat: "csv",
  theme: "light",
  maxResults: 50,
  extractionDelay: 1e3,
  enableAdvancedFilters: false,
  autoBackup: false
};
const Options = () => {
  const [settings, setSettings] = reactExports.useState(defaultSettings);
  const [saving, setSaving] = reactExports.useState(false);
  const [showResetDialog, setShowResetDialog] = reactExports.useState(false);
  const [saveMessage, setSaveMessage] = reactExports.useState(null);
  reactExports.useEffect(() => {
    loadSettings();
  }, []);
  const loadSettings = async () => {
    try {
      if (typeof chrome !== "undefined" && chrome.storage) {
        const result = await chrome.storage.sync.get("extensionSettings");
        if (result.extensionSettings) {
          setSettings({ ...defaultSettings, ...result.extensionSettings });
        }
      }
    } catch (error) {
      console.error("Failed to load settings:", error);
    }
  };
  const saveSettings = async () => {
    setSaving(true);
    try {
      if (typeof chrome !== "undefined" && chrome.storage) {
        await chrome.storage.sync.set({ extensionSettings: settings });
        setSaveMessage("Settings saved successfully!");
        setTimeout(() => setSaveMessage(null), 3e3);
      }
    } catch (error) {
      console.error("Failed to save settings:", error);
      setSaveMessage("Failed to save settings. Please try again.");
      setTimeout(() => setSaveMessage(null), 3e3);
    } finally {
      setSaving(false);
    }
  };
  const resetSettings = async () => {
    setSettings(defaultSettings);
    setShowResetDialog(false);
    await saveSettings();
  };
  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "gmaps-extractor-settings.json";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  const importSettings = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result);
          setSettings({ ...defaultSettings, ...importedSettings });
        } catch (error) {
          console.error("Failed to import settings:", error);
          setSaveMessage("Failed to import settings. Invalid file format.");
          setTimeout(() => setSaveMessage(null), 3e3);
        }
      };
      reader.readAsText(file);
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(ThemeProvider, { theme: lightTheme, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(CssBaseline, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Container, { maxWidth: "md", sx: { py: 4 }, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Box, { sx: { mb: 4 }, children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Typography, { variant: "h4", component: "h1", gutterBottom: true, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(SettingsIcon, { sx: { mr: 2, verticalAlign: "middle" } }),
          "G Maps Extractor Pro - Settings"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Typography, { variant: "body1", color: "text.secondary", children: "Configure your extension preferences and behavior" })
      ] }),
      saveMessage && /* @__PURE__ */ jsxRuntimeExports.jsx(
        Alert,
        {
          severity: saveMessage.includes("Failed") ? "error" : "success",
          sx: { mb: 3 },
          children: saveMessage
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Card, { sx: { mb: 3 }, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Typography, { variant: "h6", gutterBottom: true, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(NotificationsIcon, { sx: { mr: 1, verticalAlign: "middle" } }),
          "General Settings"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Grid, { container: true, spacing: 3, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 6, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            FormControlLabel,
            {
              control: /* @__PURE__ */ jsxRuntimeExports.jsx(
                Switch,
                {
                  checked: settings.autoSave,
                  onChange: (e) => setSettings({ ...settings, autoSave: e.target.checked })
                }
              ),
              label: "Auto-save extracted data"
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 6, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            FormControlLabel,
            {
              control: /* @__PURE__ */ jsxRuntimeExports.jsx(
                Switch,
                {
                  checked: settings.notifications,
                  onChange: (e) => setSettings({ ...settings, notifications: e.target.checked })
                }
              ),
              label: "Show notifications"
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 6, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            FormControlLabel,
            {
              control: /* @__PURE__ */ jsxRuntimeExports.jsx(
                Switch,
                {
                  checked: settings.enableAdvancedFilters,
                  onChange: (e) => setSettings({ ...settings, enableAdvancedFilters: e.target.checked })
                }
              ),
              label: "Enable advanced filters"
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 6, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            FormControlLabel,
            {
              control: /* @__PURE__ */ jsxRuntimeExports.jsx(
                Switch,
                {
                  checked: settings.autoBackup,
                  onChange: (e) => setSettings({ ...settings, autoBackup: e.target.checked })
                }
              ),
              label: "Auto backup data"
            }
          ) })
        ] })
      ] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Card, { sx: { mb: 3 }, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Typography, { variant: "h6", gutterBottom: true, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(DownloadIcon, { sx: { mr: 1, verticalAlign: "middle" } }),
          "Extraction Settings"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Grid, { container: true, spacing: 3, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 6, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            TextField,
            {
              fullWidth: true,
              label: "Max Results per Search",
              type: "number",
              value: settings.maxResults,
              onChange: (e) => setSettings({ ...settings, maxResults: parseInt(e.target.value) || 50 }),
              inputProps: { min: 1, max: 200 },
              helperText: "Maximum number of businesses to extract per search"
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 6, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            TextField,
            {
              fullWidth: true,
              label: "Extraction Delay (ms)",
              type: "number",
              value: settings.extractionDelay,
              onChange: (e) => setSettings({ ...settings, extractionDelay: parseInt(e.target.value) || 1e3 }),
              inputProps: { min: 500, max: 5e3 },
              helperText: "Delay between extractions to avoid rate limiting"
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 6, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(FormControl, { fullWidth: true, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(InputLabel, { children: "Default Export Format" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              Select,
              {
                value: settings.defaultExportFormat,
                onChange: (e) => setSettings({ ...settings, defaultExportFormat: e.target.value }),
                label: "Default Export Format",
                children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(MenuItem, { value: "csv", children: "CSV" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(MenuItem, { value: "excel", children: "Excel" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(MenuItem, { value: "json", children: "JSON" })
                ]
              }
            )
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 6, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(FormControl, { fullWidth: true, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(InputLabel, { children: "Theme" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              Select,
              {
                value: settings.theme,
                onChange: (e) => setSettings({ ...settings, theme: e.target.value }),
                label: "Theme",
                children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(MenuItem, { value: "light", children: "Light" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(MenuItem, { value: "dark", children: "Dark" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(MenuItem, { value: "auto", children: "Auto" })
                ]
              }
            )
          ] }) })
        ] })
      ] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Card, { sx: { mb: 3 }, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Typography, { variant: "h6", gutterBottom: true, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(SecurityIcon, { sx: { mr: 1, verticalAlign: "middle" } }),
          "Data Management"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Grid, { container: true, spacing: 2, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 4, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              fullWidth: true,
              variant: "outlined",
              startIcon: /* @__PURE__ */ jsxRuntimeExports.jsx(DownloadIcon, {}),
              onClick: exportSettings,
              children: "Export Settings"
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 4, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
            Button,
            {
              fullWidth: true,
              variant: "outlined",
              component: "label",
              startIcon: /* @__PURE__ */ jsxRuntimeExports.jsx(UploadIcon, {}),
              children: [
                "Import Settings",
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "input",
                  {
                    type: "file",
                    hidden: true,
                    accept: ".json",
                    onChange: importSettings
                  }
                )
              ]
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Grid, { size: 4, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              fullWidth: true,
              variant: "outlined",
              color: "error",
              startIcon: /* @__PURE__ */ jsxRuntimeExports.jsx(RestoreIcon, {}),
              onClick: () => setShowResetDialog(true),
              children: "Reset to Default"
            }
          ) })
        ] })
      ] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Box, { sx: { display: "flex", gap: 2, justifyContent: "flex-end" }, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        Button,
        {
          variant: "contained",
          startIcon: /* @__PURE__ */ jsxRuntimeExports.jsx(SaveIcon, {}),
          onClick: saveSettings,
          disabled: saving,
          size: "large",
          children: saving ? "Saving..." : "Save Settings"
        }
      ) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, { open: showResetDialog, onClose: () => setShowResetDialog(false), children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: "Reset Settings" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(DialogContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(Typography, { children: "Are you sure you want to reset all settings to their default values? This action cannot be undone." }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogActions, { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: () => setShowResetDialog(false), children: "Cancel" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: resetSettings, color: "error", variant: "contained", children: "Reset" })
        ] })
      ] })
    ] })
  ] });
};
const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(/* @__PURE__ */ jsxRuntimeExports.jsx(Options, {}));
