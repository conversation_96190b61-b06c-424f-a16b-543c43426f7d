(function() {
  "use strict";
  window.GMapsInternalAPI = {
    // Get search results from internal state
    getSearchResults: function() {
      try {
        const app = window.APP_INITIALIZATION_STATE;
        if (app && app[3] && app[3][6]) {
          return app[3][6];
        }
        const searchResults = document.querySelectorAll("[data-result-index]");
        return Array.from(searchResults).map((el, index) => ({
          index,
          element: el,
          data: el.__reactInternalInstance || el._reactInternalFiber
        }));
      } catch (error) {
        console.error("Failed to get search results from internal state:", error);
        return [];
      }
    },
    // Get business data from place ID
    getBusinessData: function(placeId) {
      var _a, _b, _c, _d;
      try {
        const cache = (_d = (_c = (_b = (_a = window.google) == null ? void 0 : _a.maps) == null ? void 0 : _b.places) == null ? void 0 : _c.PlacesService) == null ? void 0 : _d.cache;
        if (cache && cache[placeId]) {
          return cache[placeId];
        }
        const pageState = window.APP_INITIALIZATION_STATE;
        if (pageState) {
          const findBusinessData = (obj) => {
            if (typeof obj !== "object" || obj === null) return null;
            for (const key in obj) {
              if (key.includes("place") || key.includes("business")) {
                return obj[key];
              }
              const result = findBusinessData(obj[key]);
              if (result) return result;
            }
            return null;
          };
          return findBusinessData(pageState);
        }
        return null;
      } catch (error) {
        console.error("Failed to get business data:", error);
        return null;
      }
    },
    // Intercept network requests to capture data
    interceptRequests: function() {
      try {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
          const url = args[0];
          if (url.includes("maps.googleapis.com") || url.includes("google.com/maps")) {
            console.log("Intercepted Maps API call:", url);
          }
          return originalFetch.apply(this, args).then((response) => {
            const clonedResponse = response.clone();
            if (url.includes("search") || url.includes("place")) {
              clonedResponse.json().then((data) => {
                console.log("Maps API response data:", data);
                window.GMapsAPIData = window.GMapsAPIData || [];
                window.GMapsAPIData.push({ url, data, timestamp: Date.now() });
              }).catch(() => {
              });
            }
            return response;
          });
        };
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
          const xhr = new originalXHR();
          const originalOpen = xhr.open;
          const originalSend = xhr.send;
          xhr.open = function(method, url, ...args) {
            this._url = url;
            return originalOpen.apply(this, [method, url, ...args]);
          };
          xhr.send = function(data) {
            const url = this._url;
            if (url && (url.includes("maps.googleapis.com") || url.includes("google.com/maps"))) {
              console.log("Intercepted XHR Maps API call:", url);
              const originalOnLoad = this.onload;
              this.onload = function() {
                try {
                  if (url.includes("search") || url.includes("place")) {
                    const responseData = JSON.parse(this.responseText);
                    console.log("XHR Maps API response data:", responseData);
                    window.GMapsAPIData = window.GMapsAPIData || [];
                    window.GMapsAPIData.push({ url, data: responseData, timestamp: Date.now() });
                  }
                } catch (error) {
                }
                if (originalOnLoad) {
                  originalOnLoad.apply(this, arguments);
                }
              };
            }
            return originalSend.apply(this, [data]);
          };
          return xhr;
        };
        console.log("Request interception setup complete");
      } catch (error) {
        console.error("Failed to setup request interception:", error);
      }
    },
    // Extract data from internal state
    extractFromInternalState: function() {
      try {
        const results = [];
        const methods = [
          () => this.getSearchResults(),
          () => window.GMapsAPIData || [],
          () => {
            const reactElements = document.querySelectorAll("[data-testid], [data-value]");
            return Array.from(reactElements).map((el) => {
              var _a;
              return {
                testId: el.dataset.testid,
                value: el.dataset.value,
                text: (_a = el.textContent) == null ? void 0 : _a.trim(),
                element: el
              };
            }).filter((item) => item.text);
          }
        ];
        for (const method of methods) {
          try {
            const data = method();
            if (data && data.length > 0) {
              results.push(...data);
            }
          } catch (error) {
            console.error("Method failed:", error);
          }
        }
        return results;
      } catch (error) {
        console.error("Failed to extract from internal state:", error);
        return [];
      }
    }
  };
  window.GMapsInternalAPI.interceptRequests();
  console.log("Google Maps Internal API access injected");
})();
