class ChromeStorage {
  static async get(key) {
    try {
      if (typeof chrome === "undefined" || !chrome.storage) {
        return null;
      }
      const result = await chrome.storage.local.get(key);
      return result[key] || null;
    } catch (error) {
      console.error(`Failed to get ${key} from storage:`, error);
      return null;
    }
  }
  static async set(key, value) {
    try {
      if (typeof chrome === "undefined" || !chrome.storage) {
        return false;
      }
      await chrome.storage.local.set({ [key]: value });
      return true;
    } catch (error) {
      console.error(`Failed to set ${key} in storage:`, error);
      return false;
    }
  }
  static async remove(key) {
    try {
      if (typeof chrome === "undefined" || !chrome.storage) {
        return false;
      }
      await chrome.storage.local.remove(key);
      return true;
    } catch (error) {
      console.error(`Failed to remove ${key} from storage:`, error);
      return false;
    }
  }
  static async clear() {
    try {
      if (typeof chrome === "undefined" || !chrome.storage) {
        return false;
      }
      await chrome.storage.local.clear();
      return true;
    } catch (error) {
      console.error("Failed to clear storage:", error);
      return false;
    }
  }
  // Specific storage methods
  static async saveBusinessData(businesses) {
    return this.set("extractedData", businesses);
  }
  static async getBusinessData() {
    const data = await this.get("extractedData");
    return data || [];
  }
  static async saveUser(user) {
    return this.set("user", user);
  }
  static async getUser() {
    return this.get("user");
  }
  static async saveSettings(settings) {
    return this.set("settings", settings);
  }
  static async getSettings() {
    return this.get("settings");
  }
}
class ChromeMessaging {
  static async sendToContentScript(tabId, message, timeout = 3e4) {
    try {
      if (typeof chrome === "undefined" || !chrome.tabs) {
        throw new Error("Chrome tabs API not available");
      }
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Message timeout")), timeout);
      });
      const messagePromise = chrome.tabs.sendMessage(tabId, message);
      return await Promise.race([messagePromise, timeoutPromise]);
    } catch (error) {
      console.error("Failed to send message to content script:", error);
      throw error;
    }
  }
  static async sendToBackground(message) {
    try {
      if (typeof chrome === "undefined" || !chrome.runtime) {
        return null;
      }
      return await chrome.runtime.sendMessage(message);
    } catch (error) {
      console.error("Failed to send message to background:", error);
      return null;
    }
  }
  static onMessage(callback) {
    if (typeof chrome !== "undefined" && chrome.runtime) {
      chrome.runtime.onMessage.addListener(callback);
    }
  }
  static removeMessageListener(callback) {
    if (typeof chrome !== "undefined" && chrome.runtime) {
      chrome.runtime.onMessage.removeListener(callback);
    }
  }
}
class ChromeTabs {
  static async getCurrentTab() {
    try {
      if (typeof chrome === "undefined" || !chrome.tabs) {
        return null;
      }
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      return tab || null;
    } catch (error) {
      console.error("Failed to get current tab:", error);
      return null;
    }
  }
  static async isGoogleMapsTab(tab) {
    const currentTab = tab || await this.getCurrentTab();
    if (!currentTab || !currentTab.url) {
      return false;
    }
    const googleMapsPatterns = [
      /^https:\/\/www\.google\.com\/maps/,
      /^https:\/\/maps\.google\.com/,
      /^https:\/\/www\.google\.[a-z]+\/maps/,
      /^https:\/\/maps\.google\.[a-z]+/
    ];
    return googleMapsPatterns.some((pattern) => pattern.test(currentTab.url));
  }
  static async openGoogleMaps(query) {
    try {
      if (typeof chrome === "undefined" || !chrome.tabs) {
        return null;
      }
      let url = "https://www.google.com/maps";
      if (query) {
        url += `/search/${encodeURIComponent(query)}`;
      }
      const tab = await chrome.tabs.create({ url });
      return tab;
    } catch (error) {
      console.error("Failed to open Google Maps:", error);
      return null;
    }
  }
  static async injectScript(tabId, file) {
    try {
      if (typeof chrome === "undefined") {
        throw new Error("Chrome API not available");
      }
      if (chrome.scripting) {
        try {
          await chrome.scripting.executeScript({
            target: { tabId },
            files: [file]
          });
          console.log("Script injected successfully using chrome.scripting");
          return true;
        } catch (scriptingError) {
          console.warn("chrome.scripting failed:", scriptingError);
        }
      }
      if (chrome.tabs && chrome.tabs.executeScript) {
        try {
          await new Promise((resolve, reject) => {
            chrome.tabs.executeScript(tabId, { file }, (result) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(result);
              }
            });
          });
          console.log("Script injected successfully using chrome.tabs.executeScript");
          return true;
        } catch (tabsError) {
          console.warn("chrome.tabs.executeScript failed:", tabsError);
        }
      }
      throw new Error("No available script injection method");
    } catch (error) {
      console.error("Failed to inject script:", error);
      throw error;
    }
  }
  static async checkContentScriptInjected(tabId) {
    try {
      const response = await ChromeMessaging.sendToContentScript(tabId, {
        type: "PING",
        payload: {}
      }, 2e3);
      return response?.success === true;
    } catch (error) {
      return false;
    }
  }
  static async ensureContentScriptInjected(tabId) {
    if (await this.checkContentScriptInjected(tabId)) {
      console.log("Content script already injected and responding");
      return true;
    }
    const maxAttempts = 3;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`Attempting to inject content script (attempt ${attempt}/${maxAttempts})`);
        await this.injectScript(tabId, "content.js");
        await new Promise((resolve) => setTimeout(resolve, 2e3));
        if (await this.checkContentScriptInjected(tabId)) {
          console.log("Content script successfully injected and responding");
          return true;
        }
        console.warn(`Content script injection attempt ${attempt} failed - not responding`);
      } catch (error) {
        console.warn(`Content script injection attempt ${attempt} failed:`, error);
      }
      if (attempt < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 1e3));
      }
    }
    return false;
  }
}
class ChromeNotifications {
  static async create(title, message, type = "basic") {
    try {
      if (typeof chrome === "undefined" || !chrome.notifications) {
        return null;
      }
      return new Promise((resolve) => {
        chrome.notifications.create({
          type,
          iconUrl: "icon48.png",
          title,
          message
        }, (notificationId) => {
          resolve(notificationId || null);
        });
      });
    } catch (error) {
      console.error("Failed to create notification:", error);
      return null;
    }
  }
  static async clear(notificationId) {
    try {
      if (typeof chrome === "undefined" || !chrome.notifications) {
        return false;
      }
      return new Promise((resolve) => {
        chrome.notifications.clear(notificationId, (wasCleared) => {
          resolve(wasCleared || false);
        });
      });
    } catch (error) {
      console.error("Failed to clear notification:", error);
      return false;
    }
  }
}

class DataValidator {
  static isValidBusinessData(data) {
    return data && typeof data.id === "string" && typeof data.name === "string" && typeof data.address === "string" && typeof data.category === "string" && data.extractedAt instanceof Date && data.source === "google_maps";
  }
  static isValidReview(data) {
    return data && typeof data.id === "string" && typeof data.author === "string" && typeof data.rating === "number" && typeof data.text === "string" && data.date instanceof Date && typeof data.businessId === "string";
  }
  static sanitizeBusinessData(data) {
    try {
      const sanitized = {
        id: String(data.id || ""),
        name: String(data.name || "").trim(),
        address: String(data.address || "").trim(),
        phone: data.phone ? String(data.phone).trim() : void 0,
        website: data.website ? String(data.website).trim() : void 0,
        email: data.email ? String(data.email).trim() : void 0,
        category: String(data.category || "Unknown").trim(),
        rating: data.rating ? Number(data.rating) : void 0,
        reviewCount: data.reviewCount ? Number(data.reviewCount) : void 0,
        priceLevel: data.priceLevel ? Number(data.priceLevel) : void 0,
        hours: Array.isArray(data.hours) ? data.hours : void 0,
        photos: Array.isArray(data.photos) ? data.photos : void 0,
        reviews: Array.isArray(data.reviews) ? data.reviews : void 0,
        coordinates: data.coordinates && typeof data.coordinates === "object" ? {
          lat: Number(data.coordinates.lat),
          lng: Number(data.coordinates.lng)
        } : void 0,
        placeId: data.placeId ? String(data.placeId) : void 0,
        extractedAt: data.extractedAt instanceof Date ? data.extractedAt : /* @__PURE__ */ new Date(),
        source: "google_maps"
      };
      return this.isValidBusinessData(sanitized) ? sanitized : null;
    } catch (error) {
      console.error("Failed to sanitize business data:", error);
      return null;
    }
  }
}
class DataFilter {
  static filterBusinesses(businesses, filters) {
    return businesses.filter((business) => {
      if (filters.categories.length > 0 && !filters.categories.includes(business.category)) {
        return false;
      }
      if (business.rating !== void 0) {
        if (business.rating < filters.ratingRange[0] || business.rating > filters.ratingRange[1]) {
          return false;
        }
      }
      if (business.reviewCount !== void 0) {
        if (business.reviewCount < filters.reviewCountRange[0] || business.reviewCount > filters.reviewCountRange[1]) {
          return false;
        }
      }
      if (business.priceLevel !== void 0) {
        if (business.priceLevel < filters.priceRange[0] || business.priceLevel > filters.priceRange[1]) {
          return false;
        }
      }
      if (filters.hasPhone && !business.phone) {
        return false;
      }
      if (filters.hasWebsite && !business.website) {
        return false;
      }
      if (filters.hasEmail && !business.email) {
        return false;
      }
      if (filters.isOpen && (!business.hours || !this.isCurrentlyOpen(business.hours))) {
        return false;
      }
      return true;
    });
  }
  static isCurrentlyOpen(hours) {
    return hours.some((h) => h.isOpen);
  }
  static searchBusinesses(businesses, query) {
    if (!query.trim()) {
      return businesses;
    }
    const searchTerm = query.toLowerCase().trim();
    return businesses.filter((business) => {
      return business.name.toLowerCase().includes(searchTerm) || business.address.toLowerCase().includes(searchTerm) || business.category.toLowerCase().includes(searchTerm) || business.phone && business.phone.includes(searchTerm) || business.website && business.website.toLowerCase().includes(searchTerm);
    });
  }
}
class DataExporter {
  static async exportToCSV(businesses, options) {
    const fields = options.fields.length > 0 ? options.fields : this.getDefaultFields();
    let csv = fields.join(",") + "\n";
    for (const business of businesses) {
      const row = fields.map((field) => {
        let value = this.getFieldValue(business, field);
        if (typeof value === "string") {
          value = value.replace(/"/g, '""');
          if (value.includes(",") || value.includes("\n") || value.includes('"')) {
            value = `"${value}"`;
          }
        }
        return value || "";
      });
      csv += row.join(",") + "\n";
    }
    return csv;
  }
  static async exportToJSON(businesses, options) {
    const data = businesses.map((business) => {
      const exported = {};
      if (options.fields.length > 0) {
        for (const field of options.fields) {
          exported[field] = this.getFieldValue(business, field);
        }
      } else {
        Object.assign(exported, business);
      }
      if (options.includeReviews && business.reviews) {
        exported.reviews = business.reviews;
      }
      if (options.includePhotos && business.photos) {
        exported.photos = business.photos;
      }
      return exported;
    });
    return JSON.stringify(data, null, 2);
  }
  static async exportToExcel(businesses, options) {
    const csvData = await this.exportToCSV(businesses, options);
    return new Blob([csvData], { type: "application/vnd.ms-excel" });
  }
  static getDefaultFields() {
    return [
      "name",
      "address",
      "phone",
      "website",
      "email",
      "category",
      "rating",
      "reviewCount",
      "priceLevel",
      "extractedAt"
    ];
  }
  static getFieldValue(business, field) {
    switch (field) {
      case "coordinates":
        return business.coordinates ? `${business.coordinates.lat},${business.coordinates.lng}` : "";
      case "hours":
        return business.hours ? business.hours.map((h) => `${h.day}: ${h.hours}`).join("; ") : "";
      case "extractedAt":
        return business.extractedAt.toISOString();
      default:
        return business[field];
    }
  }
}
class DataStats {
  static getBusinessStats(businesses) {
    const total = businesses.length;
    const withPhone = businesses.filter((b) => b.phone).length;
    const withWebsite = businesses.filter((b) => b.website).length;
    const withEmail = businesses.filter((b) => b.email).length;
    const withReviews = businesses.filter((b) => b.reviews && b.reviews.length > 0).length;
    const withPhotos = businesses.filter((b) => b.photos && b.photos.length > 0).length;
    const avgRating = businesses.filter((b) => b.rating !== void 0).reduce((sum, b) => sum + (b.rating || 0), 0) / businesses.filter((b) => b.rating !== void 0).length;
    const categories = [...new Set(businesses.map((b) => b.category))];
    const categoryStats = categories.map((category) => ({
      category,
      count: businesses.filter((b) => b.category === category).length
    })).sort((a, b) => b.count - a.count);
    return {
      total,
      withPhone,
      withWebsite,
      withEmail,
      withReviews,
      withPhotos,
      avgRating: isNaN(avgRating) ? 0 : avgRating,
      categories: categoryStats,
      phonePercentage: total > 0 ? withPhone / total * 100 : 0,
      websitePercentage: total > 0 ? withWebsite / total * 100 : 0,
      emailPercentage: total > 0 ? withEmail / total * 100 : 0
    };
  }
  static getReviewStats(reviews) {
    const total = reviews.length;
    const avgRating = reviews.reduce((sum, r) => sum + r.rating, 0) / total;
    const ratingDistribution = [1, 2, 3, 4, 5].map((rating) => ({
      rating,
      count: reviews.filter((r) => r.rating === rating).length
    }));
    const avgTextLength = reviews.reduce((sum, r) => sum + r.text.length, 0) / total;
    return {
      total,
      avgRating: isNaN(avgRating) ? 0 : avgRating,
      ratingDistribution,
      avgTextLength: isNaN(avgTextLength) ? 0 : avgTextLength
    };
  }
}
function formatDate(date) {
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  });
}
function formatRating(rating) {
  return rating.toFixed(1);
}

export { ChromeTabs as C, DataFilter as D, ChromeMessaging as a, DataExporter as b, formatDate as c, DataStats as d, ChromeStorage as e, formatRating as f, ChromeNotifications as g, DataValidator as h };
