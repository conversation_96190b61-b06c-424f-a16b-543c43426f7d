// Google Maps Internal API access script
// This script provides access to Google Maps internal APIs and data

declare global {
  interface Window {
    GMapsInternalAPI: {
      getSearchResults: () => any[];
      getBusinessData: (placeId: string) => any;
      interceptRequests: () => void;
      extractFromInternalState: () => any[];
    };
  }
}

// Access Google Maps internal APIs
window.GMapsInternalAPI = {
  // Get search results from internal state
  getSearchResults: function() {
    try {
      // Try to access Google Maps internal state
      const app = (window as any).APP_INITIALIZATION_STATE;
      if (app && app[3] && app[3][6]) {
        return app[3][6]; // Search results data
      }

      // Alternative: try to get from search results container
      const searchResults = document.querySelectorAll('[data-result-index]');
      return Array.from(searchResults).map((el, index) => ({
        index,
        element: el,
        data: (el as any).__reactInternalInstance || (el as any)._reactInternalFiber
      }));
    } catch (error) {
      console.error('Failed to get search results from internal state:', error);
      return [];
    }
  },

  // Get business data from place ID
  getBusinessData: function(placeId: string) {
    try {
      // Access internal business data cache
      const cache = (window as any).google?.maps?.places?.PlacesService?.cache;
      if (cache && cache[placeId]) {
        return cache[placeId];
      }

      // Alternative: try to get from current page state
      const pageState = (window as any).APP_INITIALIZATION_STATE;
      if (pageState) {
        // Search for business data in page state
        const findBusinessData = (obj: any): any => {
          if (typeof obj !== 'object' || obj === null) return null;
          
          for (const key in obj) {
            if (key.includes('place') || key.includes('business')) {
              return obj[key];
            }
            const result = findBusinessData(obj[key]);
            if (result) return result;
          }
          return null;
        };

        return findBusinessData(pageState);
      }

      return null;
    } catch (error) {
      console.error('Failed to get business data:', error);
      return null;
    }
  },

  // Intercept network requests to capture data
  interceptRequests: function() {
    try {
      // Intercept fetch requests
      const originalFetch = window.fetch;
      window.fetch = function(...args) {
        const url = args[0] as string;
        
        // Log Google Maps API calls
        if (url.includes('maps.googleapis.com') || url.includes('google.com/maps')) {
          console.log('Intercepted Maps API call:', url);
        }

        return originalFetch.apply(this, args).then(response => {
          // Clone response to read data without consuming it
          const clonedResponse = response.clone();
          
          if (url.includes('search') || url.includes('place')) {
            clonedResponse.json().then(data => {
              console.log('Maps API response data:', data);
              // Store data for later use
              (window as any).GMapsAPIData = (window as any).GMapsAPIData || [];
              (window as any).GMapsAPIData.push({ url, data, timestamp: Date.now() });
            }).catch(() => {
              // Ignore JSON parse errors for non-JSON responses
            });
          }

          return response;
        });
      };

      // Intercept XMLHttpRequest
      const originalXHR = window.XMLHttpRequest;
      window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;

        xhr.open = function(method, url, ...args) {
          (this as any)._url = url;
          return originalOpen.apply(this, [method, url, ...args]);
        };

        xhr.send = function(data) {
          const url = (this as any)._url;
          if (url && (url.includes('maps.googleapis.com') || url.includes('google.com/maps'))) {
            console.log('Intercepted XHR Maps API call:', url);
            
            const originalOnLoad = this.onload;
            this.onload = function() {
              try {
                if (url.includes('search') || url.includes('place')) {
                  const responseData = JSON.parse(this.responseText);
                  console.log('XHR Maps API response data:', responseData);
                  // Store data for later use
                  (window as any).GMapsAPIData = (window as any).GMapsAPIData || [];
                  (window as any).GMapsAPIData.push({ url, data: responseData, timestamp: Date.now() });
                }
              } catch (error) {
                // Ignore JSON parse errors
              }
              
              if (originalOnLoad) {
                originalOnLoad.apply(this, arguments as any);
              }
            };
          }
          
          return originalSend.apply(this, [data]);
        };

        return xhr;
      };

      console.log('Request interception setup complete');
    } catch (error) {
      console.error('Failed to setup request interception:', error);
    }
  },

  // Extract data from internal state
  extractFromInternalState: function() {
    try {
      const results: any[] = [];
      
      // Try multiple methods to extract data
      const methods = [
        () => this.getSearchResults(),
        () => (window as any).GMapsAPIData || [],
        () => {
          // Try to get data from React components
          const reactElements = document.querySelectorAll('[data-testid], [data-value]');
          return Array.from(reactElements).map(el => ({
            testId: (el as HTMLElement).dataset.testid,
            value: (el as HTMLElement).dataset.value,
            text: el.textContent?.trim(),
            element: el
          })).filter(item => item.text);
        }
      ];

      for (const method of methods) {
        try {
          const data = method();
          if (data && data.length > 0) {
            results.push(...data);
          }
        } catch (error) {
          console.error('Method failed:', error);
        }
      }

      return results;
    } catch (error) {
      console.error('Failed to extract from internal state:', error);
      return [];
    }
  }
};

// Initialize request interception
window.GMapsInternalAPI.interceptRequests();

console.log('Google Maps Internal API access injected');

export {};
