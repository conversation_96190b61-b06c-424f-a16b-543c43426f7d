import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import {
  Box,
  Container,
  Tabs,
  Tab,
  Typography,
  Alert,
  CircularProgress,
  ThemeProvider,
  CssBaseline,
} from '@mui/material';
import {
  Business as BusinessIcon,
  RateReview as ReviewIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { AppProvider, useApp } from '../context/AppContext';
import { lightTheme } from '../theme';
import BusinessTab from '../components/BusinessTab';
import ReviewsTab from '../components/ReviewsTab';
import SettingsTab from '../components/SettingsTab';
import { ChromeTabs } from '../utils/chrome';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index, ...other }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`popup-tabpanel-${index}`}
      aria-labelledby={`popup-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
    </div>
  );
}

function PopupContent() {
  const { state, setCurrentTab, setError } = useApp();
  const [isGoogleMaps, setIsGoogleMaps] = useState(false);
  const [checkingTab, setCheckingTab] = useState(true);

  useEffect(() => {
    const checkCurrentTab = async () => {
      try {
        const isGMaps = await ChromeTabs.isGoogleMapsTab();
        setIsGoogleMaps(isGMaps);
      } catch (error) {
        console.error('Failed to check current tab:', error);
        setError('Failed to check current tab');
      } finally {
        setCheckingTab(false);
      }
    };

    checkCurrentTab();
  }, [setError]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    const tabs: ('business' | 'reviews' | 'settings')[] = ['business', 'reviews', 'settings'];
    setCurrentTab(tabs[newValue]);
  };

  const getCurrentTabIndex = () => {
    switch (state.currentTab) {
      case 'business': return 0;
      case 'reviews': return 1;
      case 'settings': return 2;
      default: return 0;
    }
  };

  if (checkingTab) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="sm" sx={{ width: 420, minHeight: 500, p: 0 }}>
      {/* Header */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', px: 2, py: 1 }}>
        <Typography variant="h6" component="h1" sx={{ fontWeight: 600 }}>
          G Maps Extractor Pro
        </Typography>
        <Typography variant="body2" color="text.secondary">
          v2.3.4
        </Typography>
      </Box>

      {/* Error Alert */}
      {state.error && (
        <Alert severity="error" sx={{ m: 2 }} onClose={() => setError(undefined)}>
          {state.error}
        </Alert>
      )}

      {/* Google Maps Check */}
      {!isGoogleMaps && (
        <Alert severity="warning" sx={{ m: 2 }}>
          Please navigate to Google Maps to use the extraction features.
        </Alert>
      )}

      {/* Tabs */}
      <Box sx={{ width: '100%' }}>
        <Tabs
          value={getCurrentTabIndex()}
          onChange={handleTabChange}
          aria-label="popup tabs"
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: 48,
              fontSize: '0.875rem',
            },
          }}
        >
          <Tab
            icon={<BusinessIcon />}
            label="Business Leads"
            id="popup-tab-0"
            aria-controls="popup-tabpanel-0"
          />
          <Tab
            icon={<ReviewIcon />}
            label="Reviews & Photos"
            id="popup-tab-1"
            aria-controls="popup-tabpanel-1"
          />
          <Tab
            icon={<SettingsIcon />}
            label="Settings"
            id="popup-tab-2"
            aria-controls="popup-tabpanel-2"
          />
        </Tabs>

        {/* Tab Panels */}
        <TabPanel value={getCurrentTabIndex()} index={0}>
          <BusinessTab isGoogleMaps={isGoogleMaps} />
        </TabPanel>
        <TabPanel value={getCurrentTabIndex()} index={1}>
          <ReviewsTab isGoogleMaps={isGoogleMaps} />
        </TabPanel>
        <TabPanel value={getCurrentTabIndex()} index={2}>
          <SettingsTab />
        </TabPanel>
      </Box>
    </Container>
  );
}

const Popup: React.FC = () => {
  return (
    <ThemeProvider theme={lightTheme}>
      <CssBaseline />
      <AppProvider>
        <PopupContent />
      </AppProvider>
    </ThemeProvider>
  );
};

const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(<Popup />);

