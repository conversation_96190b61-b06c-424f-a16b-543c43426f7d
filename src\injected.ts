// Google Maps Extractor - Injected Script (TypeScript)
// This script runs in the page context to access Google Maps internal APIs

// Type definitions for window extensions (for IIFE build)
interface ExtendedWindow extends Window {
  GMapsExtractorInjected?: boolean;
  GMapsExtractorAPI?: GMapsExtractorAPI;
  GMapsAPIResponses?: APIResponse[];
  APP_INITIALIZATION_STATE?: any;
  APP_OPTIONS?: any;
  _pageData?: any;
  google?: any;
  gm_authuser_data?: any;
  APP_FLAGS?: any;
}

// Cast window to extended type
const extWindow = window as ExtendedWindow;

interface BusinessData {
  id: string;
  name: string;
  address: string;
  category: string;
  rating?: number;
  reviewCount?: number;
  phone?: string;
  website?: string;
  placeId?: string;
  extractedAt: Date;
  source: string;
}

interface APIResponse {
  url: string;
  data: any;
  timestamp: number;
}

interface GMapsExtractorAPI {
  extractFromInternalState(): BusinessData[] | null;
  processStateObject(state: any): BusinessData[];
  looksLikeBusinessData(obj: any): boolean;
  extractBusinessFromObject(obj: any): BusinessData | null;
  extractCategory(obj: any): string;
  extractRating(obj: any): number | undefined;
  extractReviewCount(obj: any): number | undefined;
  generateId(): string;
  interceptNetworkRequests(): void;
  processAPIResponse(data: any, url: string): void;
  getStoredAPIResponses(): APIResponse[];
  extractFromDOM(): BusinessData[];
  extractBusinessFromDOMElement(element: Element): BusinessData | null;
  extractTextFromElement(element: Element, selectors: string[]): string;
}

(function() {
  'use strict';

  // Prevent multiple injections
  if (extWindow.GMapsExtractorInjected) {
    console.log('Google Maps Extractor injected script already loaded');
    return;
  }
  extWindow.GMapsExtractorInjected = true;

  console.log('Google Maps Extractor injected script loaded');

  // Enhanced Google Maps API access
  const GMapsExtractorAPI: GMapsExtractorAPI = {
    // Extract data from Google Maps internal state
    extractFromInternalState(): BusinessData[] | null {
      try {
        // Try to access various internal state objects
        const states = [
          extWindow.APP_INITIALIZATION_STATE,
          extWindow.APP_OPTIONS,
          extWindow._pageData,
          extWindow.google?.maps?.places,
          extWindow.gm_authuser_data,
          extWindow.APP_FLAGS
        ];

        for (const state of states) {
          if (state && typeof state === 'object') {
            console.log('Found internal state object:', state);
            const extracted = this.processStateObject(state);
            if (extracted && extracted.length > 0) {
              return extracted;
            }
          }
        }

        return null;
      } catch (error) {
        console.error('Failed to extract from internal state:', error);
        return null;
      }
    },

    // Process internal state object to extract business data
    processStateObject(state: any): BusinessData[] {
      const businesses: BusinessData[] = [];
      
      try {
        // Recursively search for business data patterns
        const searchForBusinessData = (obj: any, depth: number = 0): void => {
          if (depth > 10 || !obj || typeof obj !== 'object') return;

          // Look for arrays that might contain business data
          if (Array.isArray(obj)) {
            obj.forEach(item => searchForBusinessData(item, depth + 1));
          } else {
            // Look for business-like objects
            if (this.looksLikeBusinessData(obj)) {
              const business = this.extractBusinessFromObject(obj);
              if (business) {
                businesses.push(business);
              }
            }

            // Recursively search object properties
            Object.values(obj).forEach(value => {
              if (typeof value === 'object') {
                searchForBusinessData(value, depth + 1);
              }
            });
          }
        };

        searchForBusinessData(state);
        return businesses;
      } catch (error) {
        console.error('Error processing state object:', error);
        return [];
      }
    },

    // Check if object looks like business data
    looksLikeBusinessData(obj: any): boolean {
      if (!obj || typeof obj !== 'object') return false;

      // Look for common business data patterns
      const hasName = obj.name || obj.title || obj.displayName;
      const hasAddress = obj.address || obj.location || obj.vicinity;
      const hasRating = obj.rating || obj.averageRating;
      const hasCategory = obj.category || obj.type || obj.types;

      return !!(hasName && (hasAddress || hasRating || hasCategory));
    },

    // Extract business data from object
    extractBusinessFromObject(obj: any): BusinessData | null {
      try {
        const business: BusinessData = {
          id: this.generateId(),
          name: obj.name || obj.title || obj.displayName || '',
          address: obj.address || obj.vicinity || obj.formatted_address || '',
          category: this.extractCategory(obj),
          rating: this.extractRating(obj),
          reviewCount: this.extractReviewCount(obj),
          phone: obj.phone || obj.phoneNumber || obj.international_phone_number || '',
          website: obj.website || obj.url || '',
          placeId: obj.place_id || obj.placeId || '',
          extractedAt: new Date(),
          source: 'google_maps_internal'
        };

        // Only return if we have at least name and one other field
        if (business.name && (business.address || business.category || business.rating)) {
          return business;
        }

        return null;
      } catch (error) {
        console.error('Error extracting business from object:', error);
        return null;
      }
    },

    // Extract category from various object structures
    extractCategory(obj: any): string {
      if (obj.category) return obj.category;
      if (obj.type) return obj.type;
      if (obj.types && Array.isArray(obj.types)) {
        return obj.types[0] || '';
      }
      if (obj.businessType) return obj.businessType;
      return '';
    },

    // Extract rating from various object structures
    extractRating(obj: any): number | undefined {
      const rating = obj.rating || obj.averageRating || obj.user_ratings_total;
      return rating && !isNaN(parseFloat(rating)) ? parseFloat(rating) : undefined;
    },

    // Extract review count from various object structures
    extractReviewCount(obj: any): number | undefined {
      const count = obj.reviewCount || obj.user_ratings_total || obj.reviews_count;
      return count && !isNaN(parseInt(count)) ? parseInt(count) : undefined;
    },

    // Generate unique ID
    generateId(): string {
      return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    },

    // Monitor network requests for Google Maps API calls
    interceptNetworkRequests(): void {
      const originalFetch = window.fetch;
      const originalXHR = window.XMLHttpRequest.prototype.open;

      // Intercept fetch requests
      window.fetch = function(...args: any[]): Promise<Response> {
        const url = args[0];
        if (typeof url === 'string') {
          if (url.includes('search') || url.includes('place') || url.includes('maps/api')) {
            console.log('Intercepted fetch request:', url);
            
            // Store the promise to access response later
            const promise = originalFetch.apply(this, args);
            promise.then(response => {
              if (response.ok) {
                response.clone().text().then(text => {
                  try {
                    const data = JSON.parse(text);
                    extWindow.GMapsExtractorAPI?.processAPIResponse(data, url);
                  } catch (e) {
                    // Not JSON, ignore
                  }
                });
              }
            }).catch(error => {
              console.error('Fetch error:', error);
            });
            
            return promise;
          }
        }
        return originalFetch.apply(this, args);
      };

      // Intercept XMLHttpRequest
      window.XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]): void {
        const urlString = url.toString();
        if (urlString.includes('search') || urlString.includes('place') || urlString.includes('maps/api')) {
          console.log('Intercepted XHR request:', urlString);
          
          this.addEventListener('load', function() {
            if (this.status === 200) {
              try {
                const data = JSON.parse(this.responseText);
                extWindow.GMapsExtractorAPI?.processAPIResponse(data, urlString);
              } catch (e) {
                // Not JSON, ignore
              }
            }
          });
        }
        return originalXHR.apply(this, [method, url, ...args]);
      };

      console.log('Network request interception enabled');
    },

    // Process API response data
    processAPIResponse(data: any, url: string): void {
      try {
        console.log('Processing API response from:', url);
        
        // Store the response data for later access
        if (!extWindow.GMapsAPIResponses) {
          extWindow.GMapsAPIResponses = [];
        }

        extWindow.GMapsAPIResponses.push({
          url: url,
          data: data,
          timestamp: Date.now()
        });

        // Keep only last 50 responses to avoid memory issues
        if (extWindow.GMapsAPIResponses.length > 50) {
          extWindow.GMapsAPIResponses = extWindow.GMapsAPIResponses.slice(-50);
        }

        // Try to extract business data from the response
        const businesses = this.processStateObject(data);
        if (businesses && businesses.length > 0) {
          console.log('Extracted businesses from API response:', businesses);
          
          // Dispatch custom event with extracted data
          window.dispatchEvent(new CustomEvent('gmaps-businesses-found', {
            detail: { businesses: businesses, source: 'api_response' }
          }));
        }
      } catch (error) {
        console.error('Error processing API response:', error);
      }
    },

    // Get all stored API responses
    getStoredAPIResponses(): APIResponse[] {
      return extWindow.GMapsAPIResponses || [];
    },

    // Extract businesses from current page DOM
    extractFromDOM(): BusinessData[] {
      const businesses: BusinessData[] = [];
      
      try {
        // Enhanced selectors for current Google Maps
        const resultSelectors = [
          '[data-result-index]',
          '.Nv2PK',
          '.bfdHYd',
          '.lI9IFe',
          '.VkpGBb',
          '.THOPZb',
          '[role="article"]',
          '.section-result',
          'a[href*="/place/"]'
        ];

        for (const selector of resultSelectors) {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            const business = this.extractBusinessFromDOMElement(element);
            if (business) {
              businesses.push(business);
            }
          });
        }

        return businesses;
      } catch (error) {
        console.error('Error extracting from DOM:', error);
        return [];
      }
    },

    // Extract business from DOM element
    extractBusinessFromDOMElement(element: Element): BusinessData | null {
      try {
        const name = this.extractTextFromElement(element, [
          '.qBF1Pd',
          '.fontHeadlineSmall',
          'h3',
          '.section-result-title',
          '[data-value*="directions"]'
        ]);

        const address = this.extractTextFromElement(element, [
          '.W4Efsd:nth-child(2)',
          '.Io6YTe',
          '.rogA2c',
          '.fontBodyMedium'
        ]);

        const category = this.extractTextFromElement(element, [
          '.W4Efsd:first-child',
          '.DkEaL',
          '.fontBodySmall'
        ]);

        if (!name) return null;

        return {
          id: this.generateId(),
          name: name,
          address: address || '',
          category: category || '',
          extractedAt: new Date(),
          source: 'google_maps_dom'
        };
      } catch (error) {
        console.error('Error extracting business from DOM element:', error);
        return null;
      }
    },

    // Extract text from element using multiple selectors
    extractTextFromElement(element: Element, selectors: string[]): string {
      for (const selector of selectors) {
        const found = element.querySelector(selector);
        if (found && found.textContent && found.textContent.trim()) {
          return found.textContent.trim();
        }
      }
      return '';
    }
  };

  // Assign to window
  extWindow.GMapsExtractorAPI = GMapsExtractorAPI;

  // Initialize network interception
  GMapsExtractorAPI.interceptNetworkRequests();

  // Listen for extraction requests from content script
  window.addEventListener('message', function(event: MessageEvent) {
    if (event.source !== window) return;
    
    if (event.data.type === 'GMAPS_EXTRACT_REQUEST') {
      console.log('Received extraction request');
      
      // Try multiple extraction methods
      let businesses: BusinessData[] = [];
      
      // Method 1: Extract from internal state
      const internalData = GMapsExtractorAPI.extractFromInternalState();
      if (internalData && internalData.length > 0) {
        businesses = businesses.concat(internalData);
      }
      
      // Method 2: Extract from DOM
      const domData = GMapsExtractorAPI.extractFromDOM();
      if (domData && domData.length > 0) {
        businesses = businesses.concat(domData);
      }
      
      // Method 3: Extract from stored API responses
      const apiResponses = GMapsExtractorAPI.getStoredAPIResponses();
      apiResponses.forEach(response => {
        const apiData = GMapsExtractorAPI.processStateObject(response.data);
        if (apiData && apiData.length > 0) {
          businesses = businesses.concat(apiData);
        }
      });
      
      // Remove duplicates
      const uniqueBusinesses = businesses.filter((business, index, self) => 
        index === self.findIndex(b => b.name === business.name && b.address === business.address)
      );
      
      console.log(`Extracted ${uniqueBusinesses.length} unique businesses`);
      
      // Send response back to content script
      window.postMessage({
        type: 'GMAPS_EXTRACT_RESPONSE',
        data: uniqueBusinesses
      }, '*');
    }
  });

  console.log('Google Maps Extractor API initialized');
})();
