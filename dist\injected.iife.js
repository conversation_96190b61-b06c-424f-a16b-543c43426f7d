(function() {
  "use strict";
  const extWindow = window;
  (function() {
    if (extWindow.GMapsExtractorInjected) {
      console.log("Google Maps Extractor injected script already loaded");
      return;
    }
    extWindow.GMapsExtractorInjected = true;
    console.log("Google Maps Extractor injected script loaded");
    const GMapsExtractorAPI = {
      // Extract data from Google Maps internal state
      extractFromInternalState() {
        var _a, _b;
        try {
          const states = [
            extWindow.APP_INITIALIZATION_STATE,
            extWindow.APP_OPTIONS,
            extWindow._pageData,
            (_b = (_a = extWindow.google) == null ? void 0 : _a.maps) == null ? void 0 : _b.places,
            extWindow.gm_authuser_data,
            extWindow.APP_FLAGS
          ];
          for (const state of states) {
            if (state && typeof state === "object") {
              console.log("Found internal state object:", state);
              const extracted = this.processStateObject(state);
              if (extracted && extracted.length > 0) {
                return extracted;
              }
            }
          }
          return null;
        } catch (error) {
          console.error("Failed to extract from internal state:", error);
          return null;
        }
      },
      // Process internal state object to extract business data
      processStateObject(state) {
        const businesses = [];
        try {
          const searchForBusinessData = (obj, depth = 0) => {
            if (depth > 10 || !obj || typeof obj !== "object") return;
            if (Array.isArray(obj)) {
              obj.forEach((item) => searchForBusinessData(item, depth + 1));
            } else {
              if (this.looksLikeBusinessData(obj)) {
                const business = this.extractBusinessFromObject(obj);
                if (business) {
                  businesses.push(business);
                }
              }
              Object.values(obj).forEach((value) => {
                if (typeof value === "object") {
                  searchForBusinessData(value, depth + 1);
                }
              });
            }
          };
          searchForBusinessData(state);
          return businesses;
        } catch (error) {
          console.error("Error processing state object:", error);
          return [];
        }
      },
      // Check if object looks like business data
      looksLikeBusinessData(obj) {
        if (!obj || typeof obj !== "object") return false;
        const hasName = obj.name || obj.title || obj.displayName;
        const hasAddress = obj.address || obj.location || obj.vicinity;
        const hasRating = obj.rating || obj.averageRating;
        const hasCategory = obj.category || obj.type || obj.types;
        return !!(hasName && (hasAddress || hasRating || hasCategory));
      },
      // Extract business data from object
      extractBusinessFromObject(obj) {
        try {
          const business = {
            id: this.generateId(),
            name: obj.name || obj.title || obj.displayName || "",
            address: obj.address || obj.vicinity || obj.formatted_address || "",
            category: this.extractCategory(obj),
            rating: this.extractRating(obj),
            reviewCount: this.extractReviewCount(obj),
            phone: obj.phone || obj.phoneNumber || obj.international_phone_number || "",
            website: obj.website || obj.url || "",
            placeId: obj.place_id || obj.placeId || "",
            extractedAt: /* @__PURE__ */ new Date(),
            source: "google_maps_internal"
          };
          if (business.name && (business.address || business.category || business.rating)) {
            return business;
          }
          return null;
        } catch (error) {
          console.error("Error extracting business from object:", error);
          return null;
        }
      },
      // Extract category from various object structures
      extractCategory(obj) {
        if (obj.category) return obj.category;
        if (obj.type) return obj.type;
        if (obj.types && Array.isArray(obj.types)) {
          return obj.types[0] || "";
        }
        if (obj.businessType) return obj.businessType;
        return "";
      },
      // Extract rating from various object structures
      extractRating(obj) {
        const rating = obj.rating || obj.averageRating || obj.user_ratings_total;
        return rating && !isNaN(parseFloat(rating)) ? parseFloat(rating) : void 0;
      },
      // Extract review count from various object structures
      extractReviewCount(obj) {
        const count = obj.reviewCount || obj.user_ratings_total || obj.reviews_count;
        return count && !isNaN(parseInt(count)) ? parseInt(count) : void 0;
      },
      // Generate unique ID
      generateId() {
        return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
      },
      // Monitor network requests for Google Maps API calls
      interceptNetworkRequests() {
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest.prototype.open;
        window.fetch = function(...args) {
          const url = args[0];
          if (typeof url === "string") {
            if (url.includes("search") || url.includes("place") || url.includes("maps/api")) {
              console.log("Intercepted fetch request:", url);
              const promise = originalFetch.apply(this, args);
              promise.then((response) => {
                if (response.ok) {
                  response.clone().text().then((text) => {
                    var _a;
                    try {
                      const data = JSON.parse(text);
                      (_a = extWindow.GMapsExtractorAPI) == null ? void 0 : _a.processAPIResponse(data, url);
                    } catch (e) {
                    }
                  });
                }
              }).catch((error) => {
                console.error("Fetch error:", error);
              });
              return promise;
            }
          }
          return originalFetch.apply(this, args);
        };
        window.XMLHttpRequest.prototype.open = function(method, url, ...args) {
          const urlString = url.toString();
          if (urlString.includes("search") || urlString.includes("place") || urlString.includes("maps/api")) {
            console.log("Intercepted XHR request:", urlString);
            this.addEventListener("load", function() {
              var _a;
              if (this.status === 200) {
                try {
                  const data = JSON.parse(this.responseText);
                  (_a = extWindow.GMapsExtractorAPI) == null ? void 0 : _a.processAPIResponse(data, urlString);
                } catch (e) {
                }
              }
            });
          }
          return originalXHR.apply(this, [method, url, ...args]);
        };
        console.log("Network request interception enabled");
      },
      // Process API response data
      processAPIResponse(data, url) {
        try {
          console.log("Processing API response from:", url);
          if (!extWindow.GMapsAPIResponses) {
            extWindow.GMapsAPIResponses = [];
          }
          extWindow.GMapsAPIResponses.push({
            url,
            data,
            timestamp: Date.now()
          });
          if (extWindow.GMapsAPIResponses.length > 50) {
            extWindow.GMapsAPIResponses = extWindow.GMapsAPIResponses.slice(-50);
          }
          const businesses = this.processStateObject(data);
          if (businesses && businesses.length > 0) {
            console.log("Extracted businesses from API response:", businesses);
            window.dispatchEvent(new CustomEvent("gmaps-businesses-found", {
              detail: { businesses, source: "api_response" }
            }));
          }
        } catch (error) {
          console.error("Error processing API response:", error);
        }
      },
      // Get all stored API responses
      getStoredAPIResponses() {
        return extWindow.GMapsAPIResponses || [];
      },
      // Extract businesses from current page DOM
      extractFromDOM() {
        const businesses = [];
        try {
          const resultSelectors = [
            "[data-result-index]",
            ".Nv2PK",
            ".bfdHYd",
            ".lI9IFe",
            ".VkpGBb",
            ".THOPZb",
            '[role="article"]',
            ".section-result",
            'a[href*="/place/"]'
          ];
          for (const selector of resultSelectors) {
            const elements = document.querySelectorAll(selector);
            elements.forEach((element) => {
              const business = this.extractBusinessFromDOMElement(element);
              if (business) {
                businesses.push(business);
              }
            });
          }
          return businesses;
        } catch (error) {
          console.error("Error extracting from DOM:", error);
          return [];
        }
      },
      // Extract business from DOM element
      extractBusinessFromDOMElement(element) {
        try {
          const name = this.extractTextFromElement(element, [
            ".qBF1Pd",
            ".fontHeadlineSmall",
            "h3",
            ".section-result-title",
            '[data-value*="directions"]'
          ]);
          const address = this.extractTextFromElement(element, [
            ".W4Efsd:nth-child(2)",
            ".Io6YTe",
            ".rogA2c",
            ".fontBodyMedium"
          ]);
          const category = this.extractTextFromElement(element, [
            ".W4Efsd:first-child",
            ".DkEaL",
            ".fontBodySmall"
          ]);
          if (!name) return null;
          return {
            id: this.generateId(),
            name,
            address: address || "",
            category: category || "",
            extractedAt: /* @__PURE__ */ new Date(),
            source: "google_maps_dom"
          };
        } catch (error) {
          console.error("Error extracting business from DOM element:", error);
          return null;
        }
      },
      // Extract text from element using multiple selectors
      extractTextFromElement(element, selectors) {
        for (const selector of selectors) {
          const found = element.querySelector(selector);
          if (found && found.textContent && found.textContent.trim()) {
            return found.textContent.trim();
          }
        }
        return "";
      }
    };
    extWindow.GMapsExtractorAPI = GMapsExtractorAPI;
    GMapsExtractorAPI.interceptNetworkRequests();
    window.addEventListener("message", function(event) {
      if (event.source !== window) return;
      if (event.data.type === "GMAPS_EXTRACT_REQUEST") {
        console.log("Received extraction request");
        let businesses = [];
        const internalData = GMapsExtractorAPI.extractFromInternalState();
        if (internalData && internalData.length > 0) {
          businesses = businesses.concat(internalData);
        }
        const domData = GMapsExtractorAPI.extractFromDOM();
        if (domData && domData.length > 0) {
          businesses = businesses.concat(domData);
        }
        const apiResponses = GMapsExtractorAPI.getStoredAPIResponses();
        apiResponses.forEach((response) => {
          const apiData = GMapsExtractorAPI.processStateObject(response.data);
          if (apiData && apiData.length > 0) {
            businesses = businesses.concat(apiData);
          }
        });
        const uniqueBusinesses = businesses.filter(
          (business, index, self) => index === self.findIndex((b) => b.name === business.name && b.address === business.address)
        );
        console.log(`Extracted ${uniqueBusinesses.length} unique businesses`);
        window.postMessage({
          type: "GMAPS_EXTRACT_RESPONSE",
          data: uniqueBusinesses
        }, "*");
      }
    });
    console.log("Google Maps Extractor API initialized");
  })();
})();
